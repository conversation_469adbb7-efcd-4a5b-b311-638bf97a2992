# Creation 模块界面设计文档

## 📱 页面结构概览

Creation 模块包含以下主要页面和组件：

```
Creation Module UI Structure
├── CreationHomePage (创作主页)
│   ├── 书籍管理卡片
│   ├── 卡片管理卡片
│   └── 最近创作列表
├── BookListPage (书籍列表页)
│   ├── 搜索栏
│   ├── 筛选工具栏
│   ├── 同步状态栏
│   └── 书籍列表/网格
├── BookDetailPage (书籍详情页)
│   ├── 书籍信息头部
│   ├── 统计卡片
│   ├── 书籍简介
│   ├── 最近卡片
│   └── 操作按钮
├── BookEditPage (书籍编辑页)
│   ├── 封面选择
│   ├── 基本信息表单
│   └── 保存/取消按钮
└── CardListPage (卡片列表页)
    ├── 搜索筛选栏
    ├── 筛选对话框
    └── 卡片列表
```

## 🏠 创作主页 (CreationHomePage)

### 页面布局

```dart
// 主要结构
Scaffold(
  appBar: AppBar(
    title: '创作中心',
    actions: [刷新, 调试, 设置]
  ),
  body: SingleChildScrollView(
    children: [
      书籍管理卡片,
      卡片管理卡片,
      最近创作卡片,
    ]
  )
)
```

### 核心组件

#### 1. 书籍管理卡片
- **功能**: 显示书籍总数，跳转到书籍管理
- **设计**: 卡片式布局，左侧图标，中间信息，右侧箭头
- **交互**: 点击跳转到书籍列表页面

```dart
Widget _buildBookManagementCard() {
  return Card(
    child: ListTile(
      leading: Icon(Icons.book, color: primary),
      title: Text('书籍管理'),
      subtitle: Text('${controller.totalBooks} 本书籍'),
      trailing: Icon(Icons.arrow_forward_ios),
      onTap: () => controller.toBookListPage(),
    ),
  );
}
```

#### 2. 卡片管理卡片
- **功能**: 显示卡片总数，跳转到卡片管理
- **设计**: 与书籍管理卡片保持一致的设计风格
- **交互**: 点击跳转到卡片列表页面

#### 3. 最近创作列表
- **功能**: 显示最近创建/编辑的内容
- **设计**: 垂直列表，每项显示类型、标题、时间
- **交互**: 点击跳转到对应的详情页面

### 状态管理

```dart
class CreationController extends GetxController {
  // 统计数据
  int totalBooks = 0;
  int totalCards = 0;
  List<Map<String, dynamic>> recentCreations = [];
  
  // 加载创作统计
  Future<void> loadCreationStats() async {
    // 加载书籍统计
    // 加载卡片统计
    // 加载最近创作
  }
}
```

## 📚 书籍列表页 (BookListPage)

### 页面布局

```dart
Scaffold(
  appBar: AppBar(
    title: '书籍管理',
    bottom: PreferredSize(
      child: CreationSearchBarWithFilter(), // 搜索栏
    ),
  ),
  body: Column(
    children: [
      BookFilterBar(),    // 筛选栏
      BookToolbar(),      // 工具栏
      SyncStatusBar(),    // 同步状态栏
      Expanded(
        child: BookList(), // 书籍列表
      ),
    ],
  ),
  floatingActionButton: FloatingActionButton(), // 添加按钮
)
```

### 核心组件

#### 1. 搜索栏组件 (CreationSearchBarWithFilter)
- **功能**: 实时搜索书籍，显示筛选状态
- **设计**: 搜索框 + 筛选图标
- **交互**: 输入触发搜索，点击筛选图标显示筛选选项

```dart
class CreationSearchBarWithFilter extends StatelessWidget {
  final String hintText;
  final Function(String) onChanged;
  final VoidCallback onFilterTap;
  final bool hasActiveFilter;
  
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: hintText,
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: onChanged,
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: hasActiveFilter ? Colors.blue : null,
            ),
            onPressed: onFilterTap,
          ),
        ],
      ),
    );
  }
}
```

#### 2. 筛选栏组件 (BookFilterBar)
- **功能**: 显示当前筛选条件，快速清除筛选
- **设计**: 水平滚动的标签列表
- **状态**: 可展开/收起

#### 3. 工具栏组件 (BookToolbar)
- **功能**: 视图切换（列表/网格）、排序选择
- **设计**: 水平布局的工具按钮
- **交互**: 切换视图模式，选择排序方式

#### 4. 同步状态栏 (SyncStatusBar)
- **功能**: 显示数据同步状态和进度
- **设计**: 进度条 + 状态文本
- **状态**: 同步中、成功、失败

#### 5. 书籍列表/网格
- **列表模式**: 垂直列表，显示详细信息
- **网格模式**: 2-3列网格，显示封面和基本信息
- **交互**: 点击跳转详情，长按显示操作菜单

### 书籍卡片组件

#### 列表卡片 (BookListCard)
```dart
class BookListCard extends StatelessWidget {
  final BookModel book;
  final CreationController controller;
  
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: _buildCover(),
        title: Text(book.name ?? '未命名'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(book.brief ?? '暂无简介'),
            Row(
              children: [
                Icon(Icons.schedule, size: 14),
                Text(_formatDate(book.createdAt)),
                Spacer(),
                _buildPrivacyChip(),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          items: [
            PopupMenuItem(child: Text('编辑')),
            PopupMenuItem(child: Text('删除')),
          ],
        ),
        onTap: () => controller.toBookDetailPage(book),
      ),
    );
  }
}
```

#### 网格卡片 (BookGridCard)
```dart
class BookGridCard extends StatelessWidget {
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: _buildCover(), // 封面图片
          ),
          Expanded(
            flex: 2,
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(book.name, maxLines: 2),
                  Spacer(),
                  Row(
                    children: [
                      _buildPrivacyIcon(),
                      Spacer(),
                      _buildMoreButton(),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
```

## 📖 书籍详情页 (BookDetailPage)

### 页面布局

```dart
Scaffold(
  appBar: AppBar(
    title: '书籍详情',
    actions: [
      IconButton(icon: Icons.edit, onPressed: editBook),
      PopupMenuButton(items: [...]),
    ],
  ),
  body: RefreshIndicator(
    onRefresh: controller.onRefresh,
    child: SingleChildScrollView(
      children: [
        _buildBookHeader(),      // 书籍头部信息
        _buildStatisticsCards(), // 统计卡片
        _buildBookBrief(),       // 书籍简介
        _buildRecentCards(),     // 最近卡片
        _buildActionButtons(),   // 操作按钮
      ],
    ),
  ),
)
```

### 核心组件

#### 1. 书籍头部 (BookHeader)
- **布局**: 左侧封面，右侧书籍信息
- **信息**: 书名、隐私状态、创建时间
- **交互**: 点击封面可查看大图

#### 2. 统计卡片 (StatisticsCards)
- **内容**: 总卡片数、学习进度、最近学习时间
- **设计**: 水平滚动的统计卡片
- **样式**: 每个卡片显示数字和描述

#### 3. 书籍简介 (BookBrief)
- **功能**: 显示书籍详细描述
- **特性**: 长文本可展开/收起
- **交互**: 点击"展开/收起"切换显示状态

#### 4. 最近卡片 (RecentCards)
- **内容**: 显示最近添加的卡片
- **布局**: 水平滚动列表
- **交互**: 点击卡片跳转到卡片详情

#### 5. 操作按钮 (ActionButtons)
- **按钮**: 开始学习、管理卡片、制定计划
- **布局**: 垂直排列的主要操作按钮
- **状态**: 根据书籍状态显示不同按钮

## ✏️ 书籍编辑页 (BookEditPage)

### 页面结构
- **AppBar**: 标题 + 保存/取消按钮
- **Body**: 表单字段
- **底部**: 删除按钮（编辑模式）

### 表单字段
1. **封面选择**: 点击选择图片，支持相机和相册
2. **书籍名称**: 必填文本输入框
3. **书籍简介**: 多行文本输入框
4. **隐私设置**: 单选按钮组（公开/私有/VIP）

## 🃏 卡片列表页 (CardListPage)

### 页面布局
- **AppBar**: 标题 + 搜索栏
- **Body**: 筛选栏 + 卡片列表
- **FloatingActionButton**: 添加卡片

### 筛选功能
- **书籍筛选**: 选择特定书籍的卡片
- **类型筛选**: 按卡片类型筛选
- **状态筛选**: 按学习状态筛选

## 🎨 设计规范

### 颜色系统
- **主色**: `Theme.of(context).colorScheme.primary`
- **次要色**: `Theme.of(context).colorScheme.secondary`
- **表面色**: `Theme.of(context).colorScheme.surface`
- **错误色**: `Theme.of(context).colorScheme.error`

### 字体规范
- **大标题**: `18.sp, FontWeight.bold`
- **标题**: `16.sp, FontWeight.w600`
- **正文**: `14.sp, FontWeight.normal`
- **小字**: `12.sp, FontWeight.normal`

### 间距规范
- **大间距**: `24.h`
- **中间距**: `16.h`
- **小间距**: `8.h`
- **微间距**: `4.h`

### 圆角规范
- **卡片圆角**: `12.r`
- **按钮圆角**: `8.r`
- **输入框圆角**: `8.r`

## 📱 响应式设计

### 屏幕适配
- 使用 `flutter_screenutil` 进行屏幕适配
- 支持不同屏幕尺寸的设备
- 平板设备优化布局

### 网格列数
```dart
int _getGridCrossAxisCount() {
  final screenWidth = 1.sw;
  if (screenWidth > 600.w) {
    return 3; // 平板设备显示3列
  } else {
    return 2; // 手机设备显示2列
  }
}
```

## 🔄 状态指示

### 加载状态
- **全页加载**: 显示加载指示器
- **列表加载**: 显示骨架屏
- **按钮加载**: 按钮内显示加载动画

### 空状态
- **无数据**: 显示空状态插图和提示文字
- **搜索无结果**: 显示搜索无结果提示
- **网络错误**: 显示错误状态和重试按钮

### 同步状态
- **同步中**: 显示进度条和同步文字
- **同步成功**: 显示成功提示
- **同步失败**: 显示错误提示和重试按钮
