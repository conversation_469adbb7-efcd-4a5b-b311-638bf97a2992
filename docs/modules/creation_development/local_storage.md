# Creation 模块本地存储文档

## 📊 本地存储架构

Creation 模块采用本地优先的存储策略，使用 SQLite 作为本地数据库，通过 DAO 层提供统一的数据访问接口。

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
│  BookDataService  │  CardDataService  │  UserDataService   │
├─────────────────────────────────────────────────────────────┤
│                      DAO Layer                              │
│    BookDao        │     CardDao       │     UserDao        │
├─────────────────────────────────────────────────────────────┤
│                    Database Layer                           │
│                   SQLite Database                           │
│  books │ cards │ users │ assets │ sync_records │ ...       │
└─────────────────────────────────────────────────────────────┘
```

## 🗄️ 数据库设计

### 数据库初始化

```dart
class DatabaseService extends GetxService {
  static const String _databaseName = 'cheestack.db';
  static const int _databaseVersion = 1;
  
  Future<void> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, _databaseName);
    
    _database = await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }
}
```

### 核心表结构

#### 1. 用户表 (users)

```sql
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  username TEXT UNIQUE,
  email TEXT,
  nickname TEXT,
  avatar TEXT,
  phone TEXT,
  created_at TEXT,
  updated_at TEXT,
  synced_at TEXT,
  is_dirty INTEGER DEFAULT 0
);
```

#### 2. 书籍表 (books)

```sql
CREATE TABLE books (
  id INTEGER PRIMARY KEY,
  user_id TEXT,
  name TEXT NOT NULL,
  brief TEXT,
  cover TEXT,
  privacy TEXT DEFAULT 'private',
  created_at TEXT,
  updated_at TEXT,
  synced_at TEXT,
  is_dirty INTEGER DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES users (id)
);

-- 索引
CREATE INDEX idx_books_user_id ON books(user_id);
CREATE INDEX idx_books_name ON books(name);
CREATE INDEX idx_books_created_at ON books(created_at);
```

#### 3. 卡片表 (cards)

```sql
CREATE TABLE cards (
  id INTEGER PRIMARY KEY,
  user_id TEXT,
  book_id INTEGER,
  type TEXT DEFAULT 'basic',
  type_version INTEGER DEFAULT 1,
  title TEXT,
  question TEXT,
  answer TEXT,
  extra TEXT,
  schedule_id INTEGER,
  created_at TEXT,
  updated_at TEXT,
  synced_at TEXT,
  is_dirty INTEGER DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES users (id),
  FOREIGN KEY (book_id) REFERENCES books (id)
);

-- 索引
CREATE INDEX idx_cards_user_id ON cards(user_id);
CREATE INDEX idx_cards_book_id ON cards(book_id);
CREATE INDEX idx_cards_type ON cards(type);
CREATE INDEX idx_cards_created_at ON cards(created_at);
```

#### 4. 书籍-卡片关联表 (book_cards)

```sql
CREATE TABLE book_cards (
  id INTEGER PRIMARY KEY,
  book_id INTEGER,
  card_id INTEGER,
  order_index INTEGER DEFAULT 0,
  created_at TEXT,
  FOREIGN KEY (book_id) REFERENCES books (id) ON DELETE CASCADE,
  FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE CASCADE,
  UNIQUE(book_id, card_id)
);

-- 索引
CREATE INDEX idx_book_cards_book_id ON book_cards(book_id);
CREATE INDEX idx_book_cards_card_id ON book_cards(card_id);
```

#### 5. 资源表 (assets)

```sql
CREATE TABLE assets (
  id INTEGER PRIMARY KEY,
  user_id TEXT,
  type TEXT,
  url TEXT,
  local_path TEXT,
  filename TEXT,
  file_size INTEGER,
  mime_type TEXT,
  metadata TEXT,
  created_at TEXT,
  updated_at TEXT,
  synced_at TEXT,
  is_dirty INTEGER DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES users (id)
);
```

#### 6. 卡片资源关联表 (card_assets)

```sql
CREATE TABLE card_assets (
  id INTEGER PRIMARY KEY,
  card_id INTEGER,
  asset_id INTEGER,
  type TEXT,
  order_index INTEGER DEFAULT 0,
  created_at TEXT,
  FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE CASCADE,
  FOREIGN KEY (asset_id) REFERENCES assets (id) ON DELETE CASCADE
);
```

#### 7. 同步记录表 (sync_records)

```sql
CREATE TABLE sync_records (
  id INTEGER PRIMARY KEY,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  operation TEXT NOT NULL,  -- create/update/delete
  old_data TEXT,           -- JSON格式的旧数据
  new_data TEXT,           -- JSON格式的新数据
  sync_status INTEGER DEFAULT 0, -- 0=待同步，1=已同步，2=同步失败
  created_at TEXT,
  synced_at TEXT,
  error_message TEXT
);

-- 索引
CREATE INDEX idx_sync_records_status ON sync_records(sync_status);
CREATE INDEX idx_sync_records_table ON sync_records(table_name);
```

## 🔧 DAO 层实现

### 基础 DAO 类

```dart
abstract class BaseDao<T> {
  Database get db => DatabaseService.to.database;
  
  String get tableName;
  String get primaryKey;
  bool get useAutoIncrement => true;
  
  T fromMap(Map<String, dynamic> map);
  Map<String, dynamic> toMap(T entity);
  
  /// 插入实体
  Future<int> insert(T entity) async {
    final map = toMap(entity);
    
    // 添加时间戳
    final now = DateTime.now().toIso8601String();
    map['created_at'] = now;
    map['updated_at'] = now;
    map['is_dirty'] = 1; // 标记为未同步
    
    final id = await db.insert(tableName, map);
    
    // 添加同步记录
    await addSyncRecord('create', map[primaryKey].toString(), null, map);
    
    return id;
  }
  
  /// 更新实体
  Future<int> update(T entity) async {
    final map = toMap(entity);
    final id = map[primaryKey];
    
    // 获取旧数据
    final oldData = await findById(id);
    
    // 更新时间戳
    map['updated_at'] = DateTime.now().toIso8601String();
    map['is_dirty'] = 1; // 标记为未同步
    
    final result = await db.update(
      tableName,
      map,
      where: '$primaryKey = ?',
      whereArgs: [id],
    );
    
    // 添加同步记录
    await addSyncRecord('update', id.toString(), oldData?.toJson(), map);
    
    return result;
  }
  
  /// 删除实体
  Future<int> delete(dynamic id) async {
    // 获取旧数据
    final oldData = await findById(id);
    
    final result = await db.delete(
      tableName,
      where: '$primaryKey = ?',
      whereArgs: [id],
    );
    
    // 添加同步记录
    if (oldData != null) {
      await addSyncRecord('delete', id.toString(), oldData.toJson(), null);
    }
    
    return result;
  }
  
  /// 根据ID查找
  Future<T?> findById(dynamic id) async {
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: '$primaryKey = ?',
      whereArgs: [id],
    );
    
    if (maps.isNotEmpty) {
      return fromMap(maps.first);
    }
    return null;
  }
  
  /// 条件查询
  Future<List<T>> findWhere({
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
    
    return maps.map((map) => fromMap(map)).toList();
  }
  
  /// 添加同步记录
  Future<void> addSyncRecord(
    String operation,
    String recordId,
    Map<String, dynamic>? oldData,
    Map<String, dynamic>? newData,
  ) async {
    await db.insert('sync_records', {
      'table_name': tableName,
      'record_id': recordId,
      'operation': operation,
      'old_data': oldData != null ? jsonEncode(oldData) : null,
      'new_data': newData != null ? jsonEncode(newData) : null,
      'sync_status': 0, // 待同步
      'created_at': DateTime.now().toIso8601String(),
    });
  }
}
```

### 书籍 DAO

```dart
class BookDao extends BaseDao<BookModel> {
  @override
  String get tableName => 'books';
  
  @override
  String get primaryKey => 'id';
  
  @override
  BookModel fromMap(Map<String, dynamic> map) {
    return BookModel(
      id: map['id'],
      name: map['name'],
      brief: map['brief'],
      cover: map['cover'],
      privacy: map['privacy'],
      createdAt: map['created_at'],
      updatedAt: map['updated_at'],
    );
  }
  
  @override
  Map<String, dynamic> toMap(BookModel entity) {
    return {
      'id': entity.id,
      'name': entity.name,
      'brief': entity.brief,
      'cover': entity.cover,
      'privacy': entity.privacy,
      'created_at': entity.createdAt,
      'updated_at': entity.updatedAt,
    };
  }
  
  /// 插入书籍（需要用户ID）
  Future<int> insertWithUserId(BookModel book, String userId) async {
    final map = toMap(book);
    map['user_id'] = userId;
    
    // 添加时间戳
    final now = DateTime.now().toIso8601String();
    map['created_at'] = now;
    map['updated_at'] = now;
    map['is_dirty'] = 1; // 标记为未同步
    
    final id = await db.insert(tableName, map);
    
    // 添加同步记录
    await addSyncRecord('create', id.toString(), null, map);
    
    return id;
  }
  
  /// 根据用户ID获取书籍列表
  Future<List<BookModel>> findByUserId(String userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 按名称搜索书籍
  Future<List<BookModel>> searchByName(String userId, String keyword, {
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'user_id = ? AND name LIKE ?',
      whereArgs: [userId, '%$keyword%'],
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 检查书籍名称是否存在
  Future<bool> isNameExists(String userId, String name, {int? excludeId}) async {
    String where = 'user_id = ? AND name = ?';
    List<dynamic> whereArgs = [userId, name];
    
    if (excludeId != null) {
      where += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final List<Map<String, dynamic>> maps = await db.query(
      tableName,
      where: where,
      whereArgs: whereArgs,
      limit: 1,
    );
    
    return maps.isNotEmpty;
  }
  
  /// 更新书籍信息
  Future<bool> updateBook(int bookId, {
    String? name,
    String? brief,
    String? cover,
    String? privacy,
  }) async {
    final Map<String, dynamic> updates = {};
    
    if (name != null) updates['name'] = name;
    if (brief != null) updates['brief'] = brief;
    if (cover != null) updates['cover'] = cover;
    if (privacy != null) updates['privacy'] = privacy;
    
    if (updates.isEmpty) return false;
    
    // 获取旧数据
    final oldBook = await findById(bookId);
    
    updates['updated_at'] = DateTime.now().toIso8601String();
    updates['is_dirty'] = 1;
    
    final result = await db.update(
      tableName,
      updates,
      where: 'id = ?',
      whereArgs: [bookId],
    );
    
    // 添加同步记录
    if (oldBook != null) {
      await addSyncRecord('update', bookId.toString(), oldBook.toJson(), updates);
    }
    
    return result > 0;
  }
  
  /// 获取书籍统计信息
  Future<Map<String, int>> getBookStats(String userId) async {
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_books,
        COUNT(CASE WHEN privacy = 'public' THEN 1 END) as public_books,
        COUNT(CASE WHEN privacy = 'private' THEN 1 END) as private_books
      FROM books 
      WHERE user_id = ?
    ''', [userId]);
    
    if (result.isNotEmpty) {
      final row = result.first;
      return {
        'total_books': row['total_books'] as int,
        'public_books': row['public_books'] as int,
        'private_books': row['private_books'] as int,
      };
    }
    
    return {
      'total_books': 0,
      'public_books': 0,
      'private_books': 0,
    };
  }
}
```

### 卡片 DAO

```dart
class CardDao extends BaseDao<CardModel> {
  @override
  String get tableName => 'cards';
  
  @override
  String get primaryKey => 'id';
  
  @override
  CardModel fromMap(Map<String, dynamic> map) {
    return CardModel(
      id: map['id'],
      type: map['type'],
      typeVersion: map['type_version'],
      title: map['title'],
      question: map['question'],
      answer: map['answer'],
      extra: map['extra'] != null ? jsonDecode(map['extra']) : null,
      scheduleId: map['schedule_id'],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }
  
  @override
  Map<String, dynamic> toMap(CardModel entity) {
    return {
      'id': entity.id,
      'type': entity.type,
      'type_version': entity.typeVersion,
      'title': entity.title,
      'question': entity.question,
      'answer': entity.answer,
      'extra': entity.extra != null ? jsonEncode(entity.extra) : null,
      'schedule_id': entity.scheduleId,
      'created_at': entity.createdAt?.toIso8601String(),
      'updated_at': entity.updatedAt?.toIso8601String(),
    };
  }
  
  /// 插入卡片（需要用户ID和书籍ID）
  Future<int> insertWithIds(CardModel card, String userId, int bookId) async {
    final map = toMap(card);
    map['user_id'] = userId;
    map['book_id'] = bookId;
    
    // 添加时间戳
    final now = DateTime.now().toIso8601String();
    map['created_at'] = now;
    map['updated_at'] = now;
    map['is_dirty'] = 1; // 标记为未同步
    
    final id = await db.insert(tableName, map);
    
    // 添加同步记录
    await addSyncRecord('create', id.toString(), null, map);
    
    return id;
  }
  
  /// 根据书籍ID查找卡片
  Future<List<CardModel>> findByBookId(int bookId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'book_id = ?',
      whereArgs: [bookId],
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 搜索卡片
  Future<List<CardModel>> search(String userId, String keyword, {
    int? bookId,
    int? limit,
    int? offset,
  }) async {
    String where = 'user_id = ? AND (title LIKE ? OR question LIKE ? OR answer LIKE ?)';
    List<dynamic> whereArgs = [userId, '%$keyword%', '%$keyword%', '%$keyword%'];
    
    if (bookId != null) {
      where += ' AND book_id = ?';
      whereArgs.add(bookId);
    }
    
    return await findWhere(
      where: where,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 获取新卡片（未学习的）
  Future<List<CardModel>> findNewCards(String userId, {int? limit}) async {
    return await findWhere(
      where: 'user_id = ? AND schedule_id IS NULL',
      whereArgs: [userId],
      orderBy: 'created_at ASC',
      limit: limit,
    );
  }
  
  /// 获取卡片统计信息
  Future<Map<String, int>> getCardStats(String userId, {int? bookId}) async {
    String whereClause = 'user_id = ?';
    List<dynamic> whereArgs = [userId];
    
    if (bookId != null) {
      whereClause += ' AND book_id = ?';
      whereArgs.add(bookId);
    }
    
    final result = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_cards,
        COUNT(CASE WHEN schedule_id IS NULL THEN 1 END) as new_cards,
        COUNT(CASE WHEN schedule_id IS NOT NULL THEN 1 END) as learning_cards
      FROM cards 
      WHERE $whereClause
    ''', whereArgs);
    
    if (result.isNotEmpty) {
      final row = result.first;
      return {
        'total_cards': row['total_cards'] as int,
        'new_cards': row['new_cards'] as int,
        'learning_cards': row['learning_cards'] as int,
      };
    }
    
    return {
      'total_cards': 0,
      'new_cards': 0,
      'learning_cards': 0,
    };
  }
}
```

## 🔄 数据同步机制

### 同步记录管理

```dart
class SyncRecordDao extends BaseDao<SyncRecord> {
  @override
  String get tableName => 'sync_records';
  
  /// 获取待同步的记录
  Future<List<SyncRecord>> getPendingRecords({int? limit}) async {
    return await findWhere(
      where: 'sync_status = ?',
      whereArgs: [0], // 待同步状态
      orderBy: 'created_at ASC',
      limit: limit,
    );
  }
  
  /// 标记记录为已同步
  Future<void> markAsSynced(int recordId) async {
    await db.update(
      tableName,
      {
        'sync_status': 1,
        'synced_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [recordId],
    );
  }
  
  /// 标记记录为同步失败
  Future<void> markAsFailed(int recordId, String errorMessage) async {
    await db.update(
      tableName,
      {
        'sync_status': 2,
        'error_message': errorMessage,
      },
      where: 'id = ?',
      whereArgs: [recordId],
    );
  }
}
```

## 📊 性能优化

### 1. 索引优化
- 为常用查询字段创建索引
- 复合索引优化多字段查询
- 定期分析查询性能

### 2. 批量操作
```dart
Future<void> batchInsert(List<T> entities) async {
  await db.transaction((txn) async {
    for (final entity in entities) {
      await txn.insert(tableName, toMap(entity));
    }
  });
}
```

### 3. 连接池管理
- 合理控制数据库连接数
- 及时关闭不需要的连接
- 使用连接池复用连接

### 4. 数据分页
- 大量数据使用分页查询
- 避免一次性加载所有数据
- 实现虚拟滚动优化UI性能

## 🔧 维护和监控

### 1. 数据库版本管理
```dart
Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
  if (oldVersion < 2) {
    // 版本2的升级脚本
    await db.execute('ALTER TABLE books ADD COLUMN tags TEXT');
  }
  
  if (oldVersion < 3) {
    // 版本3的升级脚本
    await db.execute('CREATE INDEX idx_books_tags ON books(tags)');
  }
}
```

### 2. 数据完整性检查
```dart
Future<bool> checkDataIntegrity() async {
  // 检查外键约束
  // 检查数据一致性
  // 修复损坏的数据
  return true;
}
```

### 3. 清理和维护
```dart
Future<void> cleanupOldRecords() async {
  // 清理过期的同步记录
  await db.delete(
    'sync_records',
    where: 'sync_status = ? AND created_at < ?',
    whereArgs: [1, DateTime.now().subtract(Duration(days: 30)).toIso8601String()],
  );
}
```
