# Creation 模块控制器文档

## 🎮 控制器架构概览

Creation 模块的控制器层负责管理 UI 状态、处理用户交互和协调业务逻辑。采用 GetX 状态管理框架，实现响应式的状态更新和依赖注入。

```
┌─────────────────────────────────────────────────────────────┐
│                    Controller Layer                         │
├─────────────────────────────────────────────────────────────┤
│  CreationController  │  BookController  │  BookDetailCtrl   │
│                      │                  │                   │
│ - 主页状态管理        │ - 书籍列表管理    │ - 书籍详情管理     │
│ - 统计数据加载        │ - 搜索筛选       │ - 操作按钮状态     │
│ - 最近创作列表        │ - 视图切换       │ - 数据刷新        │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
│  BookDataService  │  CardDataService  │  ApiSyncService    │
└─────────────────────────────────────────────────────────────┘
```

## 📱 CreationController - 主创作控制器

### 职责范围

- 管理创作主页的状态和数据
- 加载和显示统计信息
- 处理最近创作列表
- 协调书籍和卡片的创建操作
- 管理数据同步状态

### 核心属性

```dart
class CreationController extends GetxController {
  // 统计数据
  int totalBooks = 0;
  int totalCards = 0;
  List<Map<String, dynamic>> recentCreations = [];
  
  // 书籍管理相关
  List<BookModel> bookList = [];
  List<BookModel> filteredBookList = [];
  String bookSearchKeyword = '';
  
  // 卡片管理相关
  List<CardModel> cardList = [];
  String cardSearchKeyword = '';
  
  // UI状态
  bool isLoading = false;
  bool isGridView = false;
  bool isFilterActive = false;
  
  // 同步状态
  String syncStatus = 'idle';
  double syncProgress = 0.0;
  String? syncMessage;
  
  // 服务依赖
  BookDataService? _bookDataService;
  CardDataService? _cardDataService;
  BookService _bookService = BookService();
  CardService _cardService = CardService();
}
```

### 生命周期管理

```dart
@override
void onInit() {
  super.onInit();
  Console.log('CreationController onInit');
  
  // 初始化服务依赖
  _initServices();
  
  // 加载初始数据
  _loadInitialData();
}

@override
void onReady() {
  super.onReady();
  Console.log('CreationController onReady');
  
  // 页面准备就绪后的操作
  _setupAutoRefresh();
}

@override
void onClose() {
  super.onClose();
  Console.log('CreationController onClose');
  
  // 清理资源
  _cleanup();
}

/// 初始化服务依赖
void _initServices() {
  try {
    if (Get.isRegistered<BookDataService>()) {
      _bookDataService = BookDataService.to;
    }
    if (Get.isRegistered<CardDataService>()) {
      _cardDataService = CardDataService.to;
    }
  } catch (e) {
    Console.log('Failed to initialize services: $e');
  }
}

/// 加载初始数据
Future<void> _loadInitialData() async {
  await loadCreationStats();
  await loadBookList();
}
```

### 数据加载方法

```dart
/// 加载创作统计数据
Future<void> loadCreationStats() async {
  try {
    isLoading = true;
    update();
    
    // 并行加载统计数据
    final futures = await Future.wait([
      _loadBookStats(),
      _loadCardStats(),
      _loadRecentCreations(),
    ]);
    
    Console.log('创作统计加载完成 - 书籍: $totalBooks, 卡片: $totalCards');
    
  } catch (e) {
    Console.log('Failed to load creation stats: $e');
    final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
    ShowToast.fail(friendlyMessage);
  } finally {
    isLoading = false;
    update();
  }
}

/// 加载书籍统计
Future<void> _loadBookStats() async {
  if (_bookDataService != null) {
    final stats = await _bookDataService!.getBookStats();
    totalBooks = stats['total_books'] ?? 0;
  } else {
    // 回退到网络服务
    final stats = await _bookService.getBookStats();
    totalBooks = stats['total_books'] ?? 0;
  }
}

/// 加载卡片统计
Future<void> _loadCardStats() async {
  if (_cardDataService != null) {
    final stats = await _cardDataService!.getCardStats();
    totalCards = stats['total_cards'] ?? 0;
  } else {
    // 回退到网络服务
    final stats = await _cardService.getCardStats();
    totalCards = stats['total_cards'] ?? 0;
  }
}

/// 加载最近创作
Future<void> _loadRecentCreations() async {
  recentCreations.clear();
  
  try {
    // 获取最近的书籍和卡片
    final recentBooks = await _getRecentBooks(limit: 5);
    final recentCards = await _getRecentCards(limit: 5);
    
    // 合并并按时间排序
    final allRecent = <Map<String, dynamic>>[];
    
    for (final book in recentBooks) {
      allRecent.add({
        'type': 'book',
        'data': book,
        'time': book.createdAt ?? book.updatedAt,
        'title': book.name,
        'subtitle': '书籍',
      });
    }
    
    for (final card in recentCards) {
      allRecent.add({
        'type': 'card',
        'data': card,
        'time': card.createdAt ?? card.updatedAt,
        'title': card.title ?? card.question,
        'subtitle': '卡片',
      });
    }
    
    // 按时间倒序排序
    allRecent.sort((a, b) {
      final timeA = DateTime.tryParse(a['time'] ?? '') ?? DateTime(1970);
      final timeB = DateTime.tryParse(b['time'] ?? '') ?? DateTime(1970);
      return timeB.compareTo(timeA);
    });
    
    recentCreations = allRecent.take(10).toList();
    
  } catch (e) {
    Console.log('Failed to load recent creations: $e');
  }
}
```

### 书籍管理方法

```dart
/// 加载书籍列表
Future<void> loadBookList() async {
  try {
    // 优先使用本地数据服务
    if (_bookDataService != null) {
      if (bookSearchKeyword.isEmpty) {
        bookList = await _bookDataService!.getUserBooks(
          orderBy: 'created_at DESC',
        );
      } else {
        bookList = await _bookDataService!.searchBooks(bookSearchKeyword);
      }
    } else {
      // 回退到网络服务
      bookList = await _bookService.getBookList(
        search: bookSearchKeyword.isEmpty ? null : bookSearchKeyword,
      );
    }
    _applyFiltersAndSort();
    update();
  } catch (e) {
    final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
    ShowToast.fail(friendlyMessage);
  }
}

/// 应用筛选和排序
void _applyFiltersAndSort() {
  filteredBookList = List.from(bookList);
  
  // 应用筛选条件
  if (isFilterActive) {
    // 这里可以添加具体的筛选逻辑
    // 例如：按隐私设置、创建时间等筛选
  }
  
  // 应用排序
  filteredBookList.sort((a, b) {
    // 默认按创建时间倒序
    final timeA = DateTime.tryParse(a.createdAt ?? '') ?? DateTime(1970);
    final timeB = DateTime.tryParse(b.createdAt ?? '') ?? DateTime(1970);
    return timeB.compareTo(timeA);
  });
}

/// 搜索书籍
void onBookSearchChanged(String keyword) {
  bookSearchKeyword = keyword;
  loadBookList();
}

/// 切换视图模式
void toggleViewMode() {
  isGridView = !isGridView;
  update();
}

/// 切换筛选状态
void toggleFilter() {
  isFilterActive = !isFilterActive;
  _applyFiltersAndSort();
  update();
}
```

### 导航方法

```dart
/// 跳转到书籍列表页面
Future<void> toBookListPage() async {
  await Get.toNamed(AppRoutes.bookList);
}

/// 跳转到书籍详情页面
Future<void> toBookDetailPage(BookModel bookModel) async {
  await Get.toNamed(AppRoutes.bookDetail, arguments: bookModel);
}

/// 跳转到书籍编辑页面
Future<void> toBookEditPage({BookModel? bookModel}) async {
  await Get.toNamed(AppRoutes.bookEditor, arguments: bookModel)
      ?.then((e) async {
    await onRefresh();
  }).then((e) {
    update();
  });
}

/// 跳转到卡片列表页面
Future<void> toCardListPage() async {
  await Get.toNamed(AppRoutes.cardList);
}

/// 跳转到卡片创建页面
Future<void> toCardCreationPage() async {
  await Get.toNamed(AppRoutes.creationCardList);
}

/// 打开最近创作项目
void openRecentCreation(Map<String, dynamic> item) {
  if (item['type'] == 'book') {
    final bookModel = BookModel.fromJson(item['data']);
    toBookDetailPage(bookModel);
  } else if (item['type'] == 'card') {
    final cardModel = CardModel.fromJson(item['data']);
    toCardDetailPage(cardModel: cardModel);
  }
}

/// 跳转到卡片详情页面
Future<void> toCardDetailPage({required CardModel cardModel}) async {
  await Get.toNamed(AppRoutes.review, arguments: {
    "type": StudyType.info,
    "cardModel": cardModel,
  });
}
```

### 同步管理方法

```dart
/// 手动同步数据
Future<void> manualSyncData() async {
  if (syncStatus == 'syncing') {
    ShowToast.text('同步正在进行中...');
    return;
  }

  try {
    _setSyncStatus('syncing', 0.0);
    ShowToast.loading(val: '正在同步数据...');

    // 检查是否有同步服务可用
    if (!Get.isRegistered<ApiSyncService>()) {
      // 如果没有注册同步服务，先注册
      Get.put<ApiSyncService>(ApiSyncService());
      await Get.find<ApiSyncService>().init();
    }

    final apiSyncService = Get.find<ApiSyncService>();

    // 执行同步
    final success = await apiSyncService.syncAllData();

    if (success) {
      _setSyncStatus('success', 1.0);
      ShowToast.dismiss();
      ShowToast.success('数据同步成功');

      // 同步成功后重新加载数据
      await loadCreationStats();
      await loadBookList();
    } else {
      _setSyncStatus('failed', 0.0, '同步失败，请检查网络连接');
      ShowToast.dismiss();
      ShowToast.fail('数据同步失败');
    }

  } catch (e) {
    Console.log('Manual sync failed: $e');
    _setSyncStatus('failed', 0.0, e.toString());
    ShowToast.dismiss();
    ShowToast.fail('同步失败: ${ErrorHandler.getUserFriendlyMessage(e)}');
  }
}

/// 设置同步状态
void _setSyncStatus(String status, double progress, [String? message]) {
  syncStatus = status;
  syncProgress = progress;
  syncMessage = message;
  update();
}
```

### 调试和工具方法

```dart
/// 调试打印状态
void debugPrintStatus() {
  print('🔍 调试信息:');
  print('   - 总书籍数: $totalBooks');
  print('   - 总卡片数: $totalCards');
  print('   - 最近创作数量: ${recentCreations.length}');
  print('   - 最近创作内容: $recentCreations');
  print('   - 用户ID: ${AuthController.to.usr.user?.id}');
}

/// 手动刷新数据
Future<void> manualRefresh() async {
  print('🔄 手动刷新数据...');
  await loadCreationStats();
  ShowToast.success('数据刷新完成');
}

/// 刷新数据
Future<void> onRefresh() async {
  await loadCreationStats();
}

/// 测试创建书籍
Future<void> testCreateBook() async {
  try {
    // 检查服务是否可用
    Console.log('检查BookDataService是否注册: ${Get.isRegistered<BookDataService>()}');
    Console.log('检查UserDataService是否注册: ${Get.isRegistered<UserDataService>()}');

    if (!Get.isRegistered<BookDataService>()) {
      Console.log('❌ BookDataService未注册');
      return;
    }

    final bookDataService = BookDataService.to;
    final userDataService = UserDataService.to;

    Console.log('当前用户: ${userDataService.currentUser?.username}');
    Console.log('用户ID: ${userDataService.currentUser?.id}');

    if (userDataService.currentUser == null) {
      Console.log('❌ 用户未登录，无法测试');
      return;
    }

    // 测试创建书籍
    Console.log('开始测试创建书籍...');
    final result = await bookDataService.createBook(
      name: '自动测试书籍_${DateTime.now().millisecondsSinceEpoch}',
      brief: '这是一个自动测试创建的书籍',
      privacy: 'private',
    );

    if (result != null) {
      Console.log('✅ 书籍创建成功: ${result.toJson()}');
      ShowToast.success('测试书籍创建成功');
      
      // 刷新数据
      await loadCreationStats();
      await loadBookList();
    } else {
      Console.log('❌ 书籍创建失败');
      ShowToast.fail('测试书籍创建失败');
    }

  } catch (e, stackTrace) {
    Console.log('❌ 测试创建书籍时发生错误: $e');
    Console.log('错误堆栈: $stackTrace');
    ShowToast.fail('测试失败: $e');
  }
}
```

## 📖 BookDetailController - 书籍详情控制器

### 职责范围

- 管理书籍详情页面的状态
- 加载和显示书籍详细信息
- 处理书籍相关操作（编辑、删除、分享等）
- 管理书籍统计数据
- 处理学习相关功能

### 核心属性和方法

```dart
class BookDetailController extends GetxController {
  // 书籍数据
  late BookModel bookModel;
  
  // 统计数据
  int totalCards = 0;
  int newCards = 0;
  int learningCards = 0;
  int reviewCards = 0;
  int masteredCards = 0;
  
  // UI状态
  bool isLoading = false;
  bool hasError = false;
  String? errorMessage;
  bool isBriefExpanded = false;
  
  // 学习状态
  bool hasStudyPlan = false;
  DateTime? lastStudyTime;
  double studyProgress = 0.0;
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取传递的书籍数据
    final arguments = Get.arguments;
    if (arguments is BookModel) {
      bookModel = arguments;
      loadBookDetail();
    } else {
      hasError = true;
      errorMessage = '书籍数据无效';
    }
  }
  
  /// 加载书籍详情
  Future<void> loadBookDetail() async {
    try {
      isLoading = true;
      hasError = false;
      update();
      
      // 并行加载数据
      await Future.wait([
        _loadBookStats(),
        _loadStudyInfo(),
        _loadRecentCards(),
      ]);
      
    } catch (e) {
      Console.log('Failed to load book detail: $e');
      hasError = true;
      errorMessage = ErrorHandler.getUserFriendlyMessage(e);
    } finally {
      isLoading = false;
      update();
    }
  }
  
  /// 开始学习
  Future<void> startStudy() async {
    if (totalCards == 0) {
      ShowToast.text('该书籍还没有卡片，请先添加卡片');
      return;
    }

    // 如果还没有制定学习计划，先跳转到制定计划页面
    if (!hasStudyPlan) {
      await setBookSchedule();
      return;
    }

    // TODO: 跳转到学习页面
    ShowToast.text('学习功能开发中...');
  }
  
  /// 编辑书籍
  void editBook() {
    Get.toNamed('/creation/books/edit', arguments: bookModel);
  }
  
  /// 切换简介展开状态
  void toggleBriefExpansion() {
    isBriefExpanded = !isBriefExpanded;
    update();
  }
  
  /// 刷新数据
  Future<void> onRefresh() async {
    await loadBookDetail();
  }
}
```

## 🎯 控制器最佳实践

### 1. 状态管理原则

- **单一职责**: 每个控制器只负责特定页面或功能的状态管理
- **响应式更新**: 使用 GetX 的响应式特性，避免手动调用 update()
- **生命周期管理**: 正确处理控制器的初始化和清理

### 2. 错误处理

```dart
/// 统一错误处理方法
void _handleError(dynamic error, [String? context]) {
  Console.log('Error in ${context ?? 'controller'}: $error');
  
  final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
  ShowToast.fail(friendlyMessage);
  
  // 记录错误日志
  ErrorLogger.log(error, context: context);
}
```

### 3. 性能优化

```dart
/// 防抖搜索
Timer? _searchTimer;

void onSearchChanged(String keyword) {
  _searchTimer?.cancel();
  _searchTimer = Timer(Duration(milliseconds: 500), () {
    _performSearch(keyword);
  });
}
```

### 4. 内存管理

```dart
@override
void onClose() {
  // 取消定时器
  _searchTimer?.cancel();
  
  // 清理监听器
  _networkSubscription?.cancel();
  
  // 清理其他资源
  super.onClose();
}
```
