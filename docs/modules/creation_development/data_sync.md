# Creation 模块数据同步文档

## 🔄 同步架构概览

Creation 模块采用本地优先的数据同步策略，确保用户在离线状态下也能正常使用所有功能。同步系统支持双向同步、冲突检测和自动重试机制。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Local Data    │    │  Sync Service   │    │  Remote API     │
│                 │    │                 │    │                 │
│ - SQLite DB     │◄──►│ - ApiSyncService│◄──►│ - REST API      │
│ - Local Files   │    │ - SyncManager   │    │ - WebSocket     │
│ - Cache         │    │ - ConflictRes   │    │ - File Storage  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 同步策略

### 1. 本地优先原则

所有用户操作都优先在本地完成，然后异步同步到云端：

```dart
// 创建书籍的流程
Future<BookModel?> createBook({required String name}) async {
  // 1. 立即保存到本地数据库
  final book = BookModel(name: name, createdAt: DateTime.now());
  final bookId = await _daoManager.bookDao.insertWithUserId(book, userId);
  
  // 2. 异步同步到云端（不等待结果）
  unawaited(_syncBookToApi(book.copyWith(id: bookId)));
  
  // 3. 立即返回本地结果
  return book.copyWith(id: bookId);
}
```

### 2. 同步时机

- **立即同步**: 用户主动触发的同步操作
- **后台同步**: 应用启动、网络恢复时自动同步
- **定时同步**: 定期检查并同步未同步的数据
- **事件驱动**: 数据变更后自动触发同步

### 3. 同步方向

- **上行同步**: 本地数据同步到云端
- **下行同步**: 云端数据同步到本地
- **双向同步**: 同时进行上行和下行同步

## 🔧 同步服务实现

### ApiSyncService 核心类

```dart
class ApiSyncService extends GetxService {
  final Rx<SyncStatus> _syncStatus = SyncStatus.idle.obs;
  final RxDouble _syncProgress = 0.0.obs;
  
  SyncStatus get syncStatus => _syncStatus.value;
  double get syncProgress => _syncProgress.value;
  
  /// 初始化同步服务
  Future<ApiSyncService> init() async {
    // 注册网络状态监听
    _setupNetworkListener();
    
    // 启动后台同步任务
    _startBackgroundSync();
    
    return this;
  }
  
  /// 执行全量数据同步
  Future<bool> syncAllData() async {
    if (_syncStatus.value == SyncStatus.syncing) {
      Console.log('同步正在进行中，跳过本次同步');
      return false;
    }
    
    try {
      _setSyncStatus(SyncStatus.syncing, 0.0);
      
      final currentUserId = UserDataService.to.currentUser?.id;
      if (currentUserId == null) {
        throw Exception('用户未登录');
      }
      
      Console.log('Starting full data sync for user: $currentUserId');
      
      // 1. 同步用户数据 (10%)
      await _syncUserData(currentUserId);
      _syncProgress.value = 0.1;
      
      // 2. 同步书籍数据 (30%)
      await _syncBooksData(currentUserId);
      _syncProgress.value = 0.3;
      
      // 3. 同步卡片数据 (60%)
      await _syncCardsData(currentUserId);
      _syncProgress.value = 0.6;
      
      // 4. 同步资源数据 (80%)
      await _syncAssetsData(currentUserId);
      _syncProgress.value = 0.8;
      
      // 5. 同步学习记录 (90%)
      await _syncStudyRecords(currentUserId);
      _syncProgress.value = 0.9;
      
      // 6. 同步书籍计划 (100%)
      await _syncBookSchedules(currentUserId);
      _syncProgress.value = 1.0;
      
      _setSyncStatus(SyncStatus.success, 1.0);
      Console.log('Full data sync completed successfully');
      return true;
      
    } catch (e) {
      Console.log('Full data sync failed: $e');
      _setSyncStatus(SyncStatus.failed, _syncProgress.value, e.toString());
      return false;
    }
  }
  
  /// 设置同步状态
  void _setSyncStatus(SyncStatus status, double progress, [String? message]) {
    _syncStatus.value = status;
    _syncProgress.value = progress;
    
    if (message != null) {
      Console.log('Sync status: $status, progress: $progress, message: $message');
    }
  }
}
```

### 书籍数据同步

```dart
/// 同步书籍数据
Future<void> _syncBooksData(String userId) async {
  try {
    Console.log('Syncing books data...');
    
    // 1. 上传本地未同步的书籍
    await _uploadLocalBooks(userId);
    
    // 2. 下载云端最新的书籍
    await _downloadRemoteBooks(userId);
    
    Console.log('Books data synced successfully');
  } catch (e) {
    Console.log('Failed to sync books data: $e');
    rethrow;
  }
}

/// 上传本地未同步的书籍
Future<void> _uploadLocalBooks(String userId) async {
  final daoManager = DaoManager.to;
  
  // 获取所有标记为dirty的书籍
  final dirtyBooks = await daoManager.bookDao.findWhere(
    where: 'user_id = ? AND is_dirty = ?',
    whereArgs: [userId, 1],
  );
  
  Console.log('Found ${dirtyBooks.length} local books to upload');
  
  for (final book in dirtyBooks) {
    try {
      // 上传到服务器
      await _uploadBookToServer(book);
      
      // 标记为已同步
      await daoManager.bookDao.db.update(
        'books',
        {
          'is_dirty': 0,
          'synced_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [book.id],
      );
      
      Console.log('Book uploaded successfully: ${book.name}');
    } catch (e) {
      Console.log('Failed to upload book ${book.name}: $e');
    }
  }
}

/// 下载云端最新的书籍
Future<void> _downloadRemoteBooks(String userId) async {
  try {
    int skip = 0;
    const int limit = 50;
    bool hasMore = true;
    
    while (hasMore) {
      final response = await OxHttp.to.get(
        '/api/v1/books',
        queryParameters: {
          'skip': skip,
          'limit': limit,
          'order': '-updated_at',
        },
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> booksData = response.data['data'] ?? [];
        
        if (booksData.isEmpty) {
          hasMore = false;
          break;
        }
        
        for (final bookData in booksData) {
          await _processRemoteBook(bookData);
        }
        
        skip += limit;
        if (booksData.length < limit) {
          hasMore = false;
        }
      } else {
        hasMore = false;
      }
    }
  } catch (e) {
    Console.log('Failed to download remote books: $e');
    rethrow;
  }
}
```

### 冲突检测和解决

```dart
/// 处理远程书籍数据
Future<void> _processRemoteBook(Map<String, dynamic> bookData) async {
  final daoManager = DaoManager.to;
  final book = BookModel.fromJson(bookData);
  
  if (book.id == null) return;
  
  // 检查本地是否存在该书籍
  final localBook = await daoManager.bookDao.findById(book.id!);
  
  if (localBook == null) {
    // 本地不存在，直接插入
    await _insertRemoteBook(book);
  } else {
    // 本地存在，检查冲突
    await _resolveBookConflict(localBook, book);
  }
}

/// 解决书籍冲突
Future<void> _resolveBookConflict(BookModel localBook, BookModel remoteBook) async {
  final daoManager = DaoManager.to;
  
  // 检查本地是否有未同步的修改
  final isDirty = await _isBookDirty(localBook.id!);
  
  if (!isDirty) {
    // 本地无修改，直接使用远程数据
    await daoManager.bookDao.update(remoteBook);
    Console.log('Book updated from remote: ${remoteBook.name}');
  } else {
    // 本地有修改，比较时间戳
    final localTime = DateTime.parse(localBook.updatedAt ?? '');
    final remoteTime = DateTime.parse(remoteBook.updatedAt ?? '');
    
    if (remoteTime.isAfter(localTime)) {
      // 远程更新，但本地也有修改 - 需要用户决策
      await _handleBookConflict(localBook, remoteBook);
    } else {
      // 本地更新，保持本地数据，但需要上传
      Console.log('Local book is newer, keeping local version: ${localBook.name}');
    }
  }
}

/// 处理书籍冲突（用户决策）
Future<void> _handleBookConflict(BookModel localBook, BookModel remoteBook) async {
  // 这里可以弹出对话框让用户选择
  // 或者采用预设的冲突解决策略
  
  // 默认策略：本地优先
  Console.log('Conflict detected for book: ${localBook.name}');
  Console.log('Using local version (local-first strategy)');
  
  // 可以记录冲突日志供后续分析
  await _logConflict('book', localBook.id!, localBook.toJson(), remoteBook.toJson());
}
```

## 📊 同步状态管理

### 同步状态枚举

```dart
enum SyncStatus {
  idle,       // 空闲状态
  syncing,    // 同步中
  success,    // 同步成功
  failed,     // 同步失败
  conflict,   // 存在冲突
}

enum SyncDirection {
  upload,     // 上传到云端
  download,   // 从云端下载
  bidirectional, // 双向同步
}
```

### 同步进度跟踪

```dart
class SyncProgress {
  final String taskName;
  final int current;
  final int total;
  final double percentage;
  final String? message;
  
  SyncProgress({
    required this.taskName,
    required this.current,
    required this.total,
    this.message,
  }) : percentage = total > 0 ? current / total : 0.0;
  
  bool get isCompleted => current >= total;
  
  @override
  String toString() {
    return 'SyncProgress(task: $taskName, progress: ${(percentage * 100).toStringAsFixed(1)}%, message: $message)';
  }
}

## 🔄 重试机制

### 指数退避重试

```dart
class RetryManager {
  static const int maxRetries = 3;
  static const Duration baseDelay = Duration(seconds: 1);

  /// 执行带重试的操作
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = RetryManager.maxRetries,
    Duration baseDelay = RetryManager.baseDelay,
  }) async {
    int attempt = 0;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempt++;

        if (attempt >= maxRetries) {
          Console.log('Operation failed after $maxRetries attempts: $e');
          rethrow;
        }

        // 指数退避延迟
        final delay = Duration(
          milliseconds: baseDelay.inMilliseconds * (1 << (attempt - 1)),
        );

        Console.log('Operation failed (attempt $attempt/$maxRetries), retrying in ${delay.inSeconds}s: $e');
        await Future.delayed(delay);
      }
    }

    throw Exception('Unexpected retry loop exit');
  }
}
```

### 网络状态监听

```dart
/// 设置网络状态监听
void _setupNetworkListener() {
  Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
    if (result != ConnectivityResult.none) {
      Console.log('Network connected, starting background sync...');
      _startBackgroundSync();
    } else {
      Console.log('Network disconnected, pausing sync operations');
    }
  });
}

/// 启动后台同步
void _startBackgroundSync() {
  // 延迟启动，避免频繁触发
  Timer(Duration(seconds: 5), () async {
    if (_syncStatus.value != SyncStatus.syncing) {
      await syncAllData();
    }
  });
}
```

## 📝 同步日志和监控

### 同步日志记录

```dart
class SyncLogger {
  static const String _logTable = 'sync_logs';

  /// 记录同步开始
  static Future<void> logSyncStart(String operation, Map<String, dynamic>? metadata) async {
    await _insertLog(
      operation: operation,
      status: 'started',
      metadata: metadata,
    );
  }

  /// 记录同步成功
  static Future<void> logSyncSuccess(String operation, Map<String, dynamic>? result) async {
    await _insertLog(
      operation: operation,
      status: 'success',
      result: result,
    );
  }

  /// 记录同步失败
  static Future<void> logSyncError(String operation, String error, Map<String, dynamic>? metadata) async {
    await _insertLog(
      operation: operation,
      status: 'error',
      error: error,
      metadata: metadata,
    );
  }

  /// 插入日志记录
  static Future<void> _insertLog({
    required String operation,
    required String status,
    String? error,
    Map<String, dynamic>? metadata,
    Map<String, dynamic>? result,
  }) async {
    final db = DatabaseService.to.database;

    await db.insert(_logTable, {
      'operation': operation,
      'status': status,
      'error': error,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'result': result != null ? jsonEncode(result) : null,
      'created_at': DateTime.now().toIso8601String(),
    });
  }
}
```

### 同步统计

```dart
class SyncStats {
  final int totalSynced;
  final int successCount;
  final int failureCount;
  final int conflictCount;
  final DateTime? lastSyncTime;
  final Duration? averageSyncTime;

  SyncStats({
    required this.totalSynced,
    required this.successCount,
    required this.failureCount,
    required this.conflictCount,
    this.lastSyncTime,
    this.averageSyncTime,
  });

  double get successRate => totalSynced > 0 ? successCount / totalSynced : 0.0;

  /// 从数据库获取同步统计
  static Future<SyncStats> fromDatabase() async {
    final db = DatabaseService.to.database;

    final result = await db.rawQuery('''
      SELECT
        COUNT(*) as total_synced,
        COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN status = 'error' THEN 1 END) as failure_count,
        COUNT(CASE WHEN status = 'conflict' THEN 1 END) as conflict_count,
        MAX(created_at) as last_sync_time
      FROM sync_logs
      WHERE created_at > datetime('now', '-30 days')
    ''');

    if (result.isNotEmpty) {
      final row = result.first;
      return SyncStats(
        totalSynced: row['total_synced'] as int,
        successCount: row['success_count'] as int,
        failureCount: row['failure_count'] as int,
        conflictCount: row['conflict_count'] as int,
        lastSyncTime: row['last_sync_time'] != null
            ? DateTime.parse(row['last_sync_time'] as String)
            : null,
      );
    }

    return SyncStats(
      totalSynced: 0,
      successCount: 0,
      failureCount: 0,
      conflictCount: 0,
    );
  }
}
```

## 🛠️ 同步配置和优化

### 同步配置

```dart
class SyncConfig {
  static const Duration syncInterval = Duration(minutes: 15);
  static const int batchSize = 50;
  static const int maxRetries = 3;
  static const Duration requestTimeout = Duration(seconds: 30);
  static const bool enableBackgroundSync = true;
  static const bool enableConflictResolution = true;

  /// 是否启用自动同步
  static bool get autoSyncEnabled =>
      Get.find<UserDataService>().currentUser?.autoSyncEnabled ?? true;

  /// 获取同步间隔
  static Duration get effectiveSyncInterval {
    // 可以根据网络状况动态调整
    final connectivity = Get.find<ConnectivityService>();
    if (connectivity.isWiFi) {
      return syncInterval;
    } else {
      return Duration(minutes: 30); // 移动网络下降低频率
    }
  }
}
```

### 性能优化

```dart
/// 批量同步优化
Future<void> _batchSyncBooks(List<BookModel> books) async {
  const int batchSize = SyncConfig.batchSize;

  for (int i = 0; i < books.length; i += batchSize) {
    final batch = books.skip(i).take(batchSize).toList();

    try {
      await _syncBookBatch(batch);
      Console.log('Synced batch ${i ~/ batchSize + 1}/${(books.length / batchSize).ceil()}');
    } catch (e) {
      Console.log('Failed to sync batch ${i ~/ batchSize + 1}: $e');
      // 继续处理下一批，不中断整个同步过程
    }
  }
}

/// 压缩传输数据
Map<String, dynamic> _compressBookData(BookModel book) {
  final data = book.toJson();

  // 移除不必要的字段
  data.removeWhere((key, value) => value == null);

  // 压缩大文本字段
  if (data['brief'] != null && (data['brief'] as String).length > 1000) {
    data['brief'] = _compressText(data['brief']);
    data['_compressed'] = true;
  }

  return data;
}
```

## 🔍 故障排查

### 常见同步问题

1. **网络连接问题**
   - 检查网络状态
   - 实现自动重连机制
   - 提供离线模式提示

2. **数据冲突**
   - 记录冲突详情
   - 提供冲突解决选项
   - 实现智能合并策略

3. **同步性能问题**
   - 监控同步耗时
   - 优化批量操作
   - 实现增量同步

### 调试工具

```dart
class SyncDebugger {
  /// 打印同步状态
  static void printSyncStatus() {
    final syncService = Get.find<ApiSyncService>();
    Console.log('=== Sync Status ===');
    Console.log('Status: ${syncService.syncStatus}');
    Console.log('Progress: ${(syncService.syncProgress * 100).toStringAsFixed(1)}%');
  }

  /// 导出同步日志
  static Future<String> exportSyncLogs() async {
    final db = DatabaseService.to.database;
    final logs = await db.query('sync_logs', orderBy: 'created_at DESC', limit: 1000);

    return jsonEncode(logs);
  }

  /// 清理同步数据
  static Future<void> clearSyncData() async {
    final db = DatabaseService.to.database;

    // 清理同步记录
    await db.delete('sync_records');

    // 重置dirty标记
    await db.update('books', {'is_dirty': 0});
    await db.update('cards', {'is_dirty': 0});

    Console.log('Sync data cleared');
  }
}
```
```
