# Creation 模块 API 接口文档

## 🌐 API 概览

Creation 模块的 API 接口负责处理书籍和卡片的 CRUD 操作，支持搜索、筛选、分页等功能。所有接口都需要用户认证，并支持权限控制。

## 🔗 基础信息

### 基础 URL
```
开发环境: https://dev-api.cheestack.com
生产环境: https://api.cheestack.com
```

### 认证方式
```http
Authorization: Bearer <access_token>
```

### 响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 📚 书籍 API

### 1. 获取书籍列表

```http
GET /v1/books
```

#### 请求参数

| 参数     | 类型   | 必填 | 说明                          |
| -------- | ------ | ---- | ----------------------------- |
| skip     | int    | 否   | 跳过数量，默认0               |
| limit    | int    | 否   | 限制数量，默认20              |
| filters  | string | 否   | JSON格式的筛选条件            |
| order_by | string | 否   | 排序字段，默认created_at DESC |

#### 筛选条件示例

```json
{
  "user_id": "user123",
  "name__icontains": "英语",
  "privacy": "private",
  "created_at__gte": "2024-01-01T00:00:00Z"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "英语单词学习",
      "brief": "常用英语单词记忆",
      "cover": "https://cdn.example.com/cover1.jpg",
      "privacy": "private",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z",
      "user": {
        "id": "user123",
        "username": "john_doe",
        "nickname": "John"
      }
    }
  ]
}
```

### 2. 创建书籍

```http
POST /v1/books
```

#### 请求体 (multipart/form-data)

| 字段    | 类型   | 必填 | 说明                       |
| ------- | ------ | ---- | -------------------------- |
| name    | string | 是   | 书籍名称，最大64字符       |
| brief   | string | 否   | 书籍简介                   |
| privacy | string | 否   | 隐私设置：free/private/vip |
| file    | file   | 否   | 封面图片文件               |

#### 请求示例

```bash
curl -X POST "https://api.cheestack.com/v1/books" \
  -H "Authorization: Bearer <token>" \
  -F "name=我的新书" \
  -F "brief=这是一本学习书籍" \
  -F "privacy=private" \
  -F "file=@cover.jpg"
```

#### 响应示例

```json
{
  "success": true,
  "message": "书籍创建成功",
  "data": {
    "id": 123,
    "name": "我的新书",
    "brief": "这是一本学习书籍",
    "cover": "https://cdn.example.com/cover123.jpg",
    "privacy": "private",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 3. 获取书籍详情

```http
GET /v1/books/{book_id}
```

#### 路径参数

| 参数    | 类型 | 必填 | 说明   |
| ------- | ---- | ---- | ------ |
| book_id | int  | 是   | 书籍ID |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 123,
    "name": "我的新书",
    "brief": "这是一本学习书籍",
    "cover": "https://cdn.example.com/cover123.jpg",
    "privacy": "private",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z",
    "stats": {
      "total_cards": 50,
      "new_cards": 10,
      "learning_cards": 20,
      "review_cards": 15,
      "mastered_cards": 5
    }
  }
}
```

### 4. 更新书籍

```http
PUT /v1/books/{book_id}
```

#### 请求体 (multipart/form-data)

| 字段    | 类型   | 必填 | 说明         |
| ------- | ------ | ---- | ------------ |
| name    | string | 否   | 书籍名称     |
| brief   | string | 否   | 书籍简介     |
| privacy | string | 否   | 隐私设置     |
| file    | file   | 否   | 新的封面图片 |

### 5. 删除书籍

```http
DELETE /v1/books/{book_id}
```

#### 响应示例

```json
{
  "success": true,
  "message": "书籍删除成功"
}
```

### 6. 批量删除书籍

```http
POST /v1/books/batch_delete
```

#### 请求体

```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

### 7. 搜索书籍

```http
GET /v1/books/search
```

#### 请求参数

| 参数    | 类型   | 必填 | 说明       |
| ------- | ------ | ---- | ---------- |
| keyword | string | 是   | 搜索关键词 |
| skip    | int    | 否   | 跳过数量   |
| limit   | int    | 否   | 限制数量   |

## 🃏 卡片 API

### 1. 获取卡片列表

```http
GET /v1/cards
```

#### 请求参数

| 参数     | 类型   | 必填 | 说明               |
| -------- | ------ | ---- | ------------------ |
| skip     | int    | 否   | 跳过数量，默认0    |
| limit    | int    | 否   | 限制数量，默认20   |
| filters  | string | 否   | JSON格式的筛选条件 |
| order_by | string | 否   | 排序字段           |

#### 筛选条件示例

```json
{
  "books__id": 123,
  "type": "basic",
  "title__icontains": "单词"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "type": "basic",
      "type_version": 1,
      "title": "英语单词：apple",
      "question": "apple",
      "answer": "苹果",
      "extra": {
        "pronunciation": "/ˈæpəl/",
        "example": "I like to eat apples."
      },
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z",
      "card_assets": [
        {
          "id": 1,
          "type": "audio",
          "url": "https://cdn.example.com/audio1.mp3",
          "filename": "apple_pronunciation.mp3"
        }
      ]
    }
  ]
}
```

### 2. 创建卡片

```http
POST /v1/cards
```

#### 请求体

```json
{
  "book_ids": [123],
  "type": "basic",
  "type_version": 1,
  "title": "英语单词：apple",
  "question": "apple",
  "answer": "苹果",
  "extra": {
    "pronunciation": "/ˈæpəl/",
    "example": "I like to eat apples."
  },
  "assets": [
    {
      "type": "audio",
      "url": "https://cdn.example.com/audio1.mp3",
      "filename": "apple_pronunciation.mp3"
    }
  ]
}
```

### 3. 获取卡片详情

```http
GET /v1/cards/{card_id}
```

### 4. 更新卡片

```http
PUT /v1/cards/{card_id}
```

### 5. 删除卡片

```http
DELETE /v1/cards/{card_id}
```

### 6. 批量操作卡片

```http
POST /v1/cards/batch_delete
POST /v1/cards/batch_move
POST /v1/cards/batch_copy
```

## 📎 资源 API

### 1. 上传文件

```http
POST /v1/assets/upload
```

#### 请求体 (multipart/form-data)

| 字段 | 类型   | 必填 | 说明                                 |
| ---- | ------ | ---- | ------------------------------------ |
| file | file   | 是   | 要上传的文件                         |
| type | string | 否   | 资源类型：image/audio/video/document |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 456,
    "url": "https://cdn.example.com/uploads/file456.jpg",
    "filename": "image.jpg",
    "file_size": 1024000,
    "mime_type": "image/jpeg",
    "type": "image"
  }
}
```

### 2. 获取资源信息

```http
GET /v1/assets/{asset_id}
```

### 3. 删除资源

```http
DELETE /v1/assets/{asset_id}
```

## 🔄 同步 API

### 1. 获取同步状态

```http
GET /v1/sync/status
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "last_sync_time": "2024-01-01T10:00:00Z",
    "pending_uploads": 5,
    "pending_downloads": 3,
    "sync_in_progress": false
  }
}
```

### 2. 触发全量同步

```http
POST /v1/sync/full
```

### 3. 触发增量同步

```http
POST /v1/sync/incremental
```

#### 请求体

```json
{
  "last_sync_time": "2024-01-01T10:00:00Z",
  "tables": ["books", "cards", "assets"]
}
```

## 📊 统计 API

### 1. 获取用户统计

```http
GET /v1/stats/user
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "total_books": 10,
    "total_cards": 500,
    "study_streak": 7,
    "total_study_time": 3600,
    "last_study_time": "2024-01-01T10:00:00Z"
  }
}
```

### 2. 获取书籍统计

```http
GET /v1/stats/books/{book_id}
```

## 🔧 前端 API 服务实现

### BookApiService

```dart
class BookApiService extends BaseApiService {
  @override
  String get basePath => '/v1/books';

  Future<ApiResponse<List<BookModel>>> getBookList({
    int skip = 0,
    int limit = 20,
    Map<String, dynamic>? filters,
    String? orderBy,
  }) async {
    final queryParameters = <String, dynamic>{
      'skip': skip,
      'limit': limit,
    };

    if (filters != null && filters.isNotEmpty) {
      queryParameters['filters'] = jsonEncode(filters);
    }

    if (orderBy != null && orderBy.isNotEmpty) {
      queryParameters['order_by'] = orderBy;
    }

    final response = await get<List<dynamic>>(
      basePath,
      queryParameters: queryParameters,
    );

    return response.map<List<BookModel>>((data) {
      return data
          .cast<Map<String, dynamic>>()
          .map((item) => BookModel.fromJson(item))
          .toList();
    });
  }

  Future<ApiResponse<BookModel>> createBook({
    required String name,
    String? brief,
    String? privacy,
    File? coverFile,
  }) async {
    final formData = FormData();
    formData.fields.add(MapEntry('name', name));
    
    if (brief != null) {
      formData.fields.add(MapEntry('brief', brief));
    }
    
    if (privacy != null) {
      formData.fields.add(MapEntry('privacy', privacy));
    }
    
    if (coverFile != null) {
      formData.files.add(MapEntry(
        'file',
        await MultipartFile.fromFile(
          coverFile.path,
          filename: basename(coverFile.path),
        ),
      ));
    }

    final response = await post<Map<String, dynamic>>(
      basePath,
      data: formData,
    );

    return response.map<BookModel>((data) {
      return BookModel.fromJson(data);
    });
  }
}
```

### CardApiService

```dart
class CardApiService extends BaseApiService {
  @override
  String get basePath => '/v1/cards';

  Future<ApiResponse<List<CardModel>>> getCardList({
    int skip = 0,
    int limit = 20,
    Map<String, dynamic>? filters,
    String? orderBy,
  }) async {
    // 实现类似 BookApiService 的逻辑
  }

  Future<ApiResponse<CardModel>> createCard({
    required List<int> bookIds,
    required String type,
    String? title,
    String? question,
    String? answer,
    Map<String, dynamic>? extra,
    List<Map<String, dynamic>>? assets,
  }) async {
    final data = {
      'book_ids': bookIds,
      'type': type,
      if (title != null) 'title': title,
      if (question != null) 'question': question,
      if (answer != null) 'answer': answer,
      if (extra != null) 'extra': extra,
      if (assets != null) 'assets': assets,
    };

    final response = await post<Map<String, dynamic>>(
      basePath,
      data: data,
    );

    return response.map<CardModel>((data) {
      return CardModel.fromJson(data);
    });
  }
}
```

## 🚨 错误处理

### 错误码定义

| 错误码 | HTTP状态码 | 说明           |
| ------ | ---------- | -------------- |
| 1001   | 400        | 请求参数错误   |
| 1002   | 401        | 未授权访问     |
| 1003   | 403        | 权限不足       |
| 1004   | 404        | 资源不存在     |
| 1005   | 409        | 资源冲突       |
| 1006   | 422        | 数据验证失败   |
| 1007   | 429        | 请求频率限制   |
| 1008   | 500        | 服务器内部错误 |

### 错误响应格式

```json
{
  "success": false,
  "error_code": 1001,
  "message": "请求参数错误",
  "details": {
    "field": "name",
    "reason": "书籍名称不能为空"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🔒 安全考虑

### 1. 认证和授权
- 所有 API 都需要有效的访问令牌
- 用户只能访问自己的数据
- 支持基于角色的权限控制

### 2. 数据验证
- 服务端进行严格的数据验证
- 防止 SQL 注入和 XSS 攻击
- 限制文件上传大小和类型

### 3. 频率限制
- 实现 API 调用频率限制
- 防止恶意请求和 DDoS 攻击
- 提供合理的错误提示

### 4. 数据加密
- 敏感数据传输使用 HTTPS
- 存储的敏感信息进行加密
- 定期更新加密密钥
