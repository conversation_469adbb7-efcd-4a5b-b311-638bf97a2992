# Creation 模块数据模型文档

## 📊 数据模型概览

Creation 模块涉及的主要数据模型包括书籍、卡片、用户、资源等。这些模型在前端和后端之间保持一致的结构，支持本地存储和云端同步。

## 📚 书籍模型 (BookModel)

### 前端模型定义

```dart
class BookModel {
  int? id;                    // 书籍ID
  String? createdAt;          // 创建时间
  String? updatedAt;          // 更新时间
  String? name;               // 书籍名称
  String? brief;              // 书籍简介
  String? cover;              // 封面图片URL
  String? privacy;            // 隐私设置 (public/private/vip)
  UserModel? user;            // 关联用户信息

  // 构造函数
  BookModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.name,
    this.brief,
    this.cover,
    this.privacy,
    this.user,
  });

  // JSON 序列化
  factory BookModel.fromJson(Map<String, Object?> json) => BookModel(
    id: json['id'] as int?,
    createdAt: json['created_at']?.toString(),
    updatedAt: json['updated_at']?.toString(),
    name: json['name']?.toString(),
    brief: json['brief']?.toString(),
    cover: json['cover']?.toString(),
    privacy: json['privacy']?.toString(),
    user: json['user'] == null ? null : UserModel.fromJson(json['user']! as Map<String, Object?>),
  );

  Map<String, Object?> toJson() => {
    'id': id,
    'created_at': createdAt,
    'updated_at': updatedAt,
    'name': name,
    'brief': brief,
    'cover': cover,
    'privacy': privacy,
    'user': user?.toJson(),
  };
}
```

### 后端模型定义

```python
# apps/study/models.py
class Book(TimeStampModelMixin):
    user = fields.ForeignKeyField(
        "models.User",
        related_name="books",
        default=None,
        null=True,
    )
    name = fields.CharField(64, description="书名", default=None, null=True)
    brief = fields.TextField(description="简介", default=None, null=True)
    cover = UrlCdnField(256, description="封面", default=None, null=True)
    privacy = fields.CharEnumField(enum_type=PrivacyType, null=False, description="类型")
    cards = fields.ManyToManyField("models.Card", through="book_card", related_name="books")
```

### 本地数据库表结构

```sql
CREATE TABLE books (
  id INTEGER PRIMARY KEY,
  user_id TEXT,
  name TEXT NOT NULL,
  brief TEXT,
  cover TEXT,
  privacy TEXT DEFAULT 'private',
  created_at TEXT,
  updated_at TEXT,
  synced_at TEXT,
  is_dirty INTEGER DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | int | 否 | 书籍唯一标识符 |
| user_id | string | 是 | 所属用户ID |
| name | string | 是 | 书籍名称，最大64字符 |
| brief | string | 否 | 书籍简介，支持长文本 |
| cover | string | 否 | 封面图片URL |
| privacy | string | 是 | 隐私设置：public/private/vip |
| created_at | string | 是 | 创建时间 ISO8601 格式 |
| updated_at | string | 是 | 更新时间 ISO8601 格式 |
| synced_at | string | 否 | 最后同步时间 |
| is_dirty | int | 是 | 是否需要同步：0=已同步，1=需要同步 |

## 🃏 卡片模型 (CardModel)

### 前端模型定义

```dart
class CardModel {
  int? id;                    // 卡片ID
  UserModel? user;            // 关联用户
  DateTime? createdAt;        // 创建时间
  DateTime? updatedAt;        // 更新时间
  String? type;               // 卡片类型
  int? typeVersion;           // 类型版本
  String? title;              // 卡片标题
  String? question;           // 问题内容
  String? answer;             // 答案内容
  dynamic extra;              // 扩展信息
  int? scheduleId;            // 学习计划ID
  List<CardAsset>? cardAssets; // 卡片资源
  bool isReviewd;             // 是否已复习

  CardModel({
    this.id,
    this.user,
    this.createdAt,
    this.updatedAt,
    this.type,
    this.typeVersion,
    this.title,
    this.question,
    this.answer,
    this.extra,
    this.scheduleId,
    this.cardAssets,
    this.isReviewd = false,
  });
}
```

### 后端模型定义

```python
class Card(TimeStampModelMixin):
    user = fields.ForeignKeyField("models.User", related_name="cards")
    likes = fields.ManyToManyField("models.User", through="card_like", related_name="liked_cards")
    assets = fields.ManyToManyField("models.Asset", through="card_asset", related_name="asset_cards")
    type = fields.CharEnumField(enum_type=CardType, null=False, default=CardType.GENERAL, description="类型")
    type_version = fields.IntField(null=False, default=1, description="版本号")
    title = fields.CharField(max_length=128, description="卡片描述", default=None, null=True)
    question = fields.TextField(null=True, description="问题")
    answer = fields.TextField(null=True, description="答案")
    extra = fields.JSONField(null=True, description="额外信息")
```

### 本地数据库表结构

```sql
CREATE TABLE cards (
  id INTEGER PRIMARY KEY,
  user_id TEXT,
  book_id INTEGER,
  type TEXT DEFAULT 'basic',
  type_version INTEGER DEFAULT 1,
  title TEXT,
  question TEXT,
  answer TEXT,
  extra TEXT,
  schedule_id INTEGER,
  created_at TEXT,
  updated_at TEXT,
  synced_at TEXT,
  is_dirty INTEGER DEFAULT 0,
  FOREIGN KEY (user_id) REFERENCES users (id),
  FOREIGN KEY (book_id) REFERENCES books (id)
);
```

### 卡片类型定义

```dart
enum CardType {
  basic,      // 基础卡片：问题-答案
  cloze,      // 填空卡片：带空白的句子
  choice,     // 选择题卡片：多选或单选
  audio,      // 音频卡片：包含音频内容
  image,      // 图片卡片：包含图片内容
  video,      // 视频卡片：包含视频内容
  compound,   // 复合卡片：多种内容类型
}
```

## 👤 用户模型 (UserModel)

### 前端模型定义

```dart
class UserModel {
  String? id;                 // 用户ID
  String? username;           // 用户名
  String? email;              // 邮箱
  String? avatar;             // 头像URL
  String? nickname;           // 昵称
  DateTime? createdAt;        // 创建时间
  DateTime? updatedAt;        // 更新时间

  UserModel({
    this.id,
    this.username,
    this.email,
    this.avatar,
    this.nickname,
    this.createdAt,
    this.updatedAt,
  });
}
```

## 📎 卡片资源模型 (CardAsset)

### 前端模型定义

```dart
class CardAsset {
  int? id;                    // 资源ID
  int? cardId;                // 关联卡片ID
  int? assetId;               // 资源ID
  String? type;               // 资源类型
  String? url;                // 资源URL
  String? filename;           // 文件名
  int? fileSize;              // 文件大小
  String? mimeType;           // MIME类型
  Map<String, dynamic>? metadata; // 元数据

  CardAsset({
    this.id,
    this.cardId,
    this.assetId,
    this.type,
    this.url,
    this.filename,
    this.fileSize,
    this.mimeType,
    this.metadata,
  });
}
```

### 资源类型定义

```dart
enum AssetType {
  image,      // 图片资源
  audio,      // 音频资源
  video,      // 视频资源
  document,   // 文档资源
  other,      // 其他资源
}
```

## 🔄 同步记录模型 (SyncRecord)

### 本地同步记录表

```sql
CREATE TABLE sync_records (
  id INTEGER PRIMARY KEY,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  operation TEXT NOT NULL,  -- create/update/delete
  old_data TEXT,           -- JSON格式的旧数据
  new_data TEXT,           -- JSON格式的新数据
  sync_status INTEGER DEFAULT 0, -- 0=待同步，1=已同步，2=同步失败
  created_at TEXT,
  synced_at TEXT,
  error_message TEXT
);
```

### 同步状态定义

```dart
enum SyncStatus {
  pending,    // 待同步
  synced,     // 已同步
  failed,     // 同步失败
}

enum SyncOperation {
  create,     // 创建操作
  update,     // 更新操作
  delete,     // 删除操作
}
```

## 📋 书籍-卡片关联模型

### 后端关联表

```python
class BookCard(Model):
    order = fields.IntField(description="卡片在书中的顺序", default=0)
    book = fields.ForeignKeyField(
        "models.Book",
        on_delete=fields.CASCADE,
        related_name="book_card",
    )
    card = fields.ForeignKeyField(
        "models.Card",
        on_delete=fields.CASCADE,
        related_name="book_card",
    )

    class Meta:
        table = "book_card"
```

### 本地关联表

```sql
CREATE TABLE book_cards (
  id INTEGER PRIMARY KEY,
  book_id INTEGER,
  card_id INTEGER,
  order_index INTEGER DEFAULT 0,
  created_at TEXT,
  FOREIGN KEY (book_id) REFERENCES books (id) ON DELETE CASCADE,
  FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE CASCADE,
  UNIQUE(book_id, card_id)
);
```

## 🔍 数据验证规则

### 书籍验证

```dart
class BookValidator {
  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return '书籍名称不能为空';
    }
    if (name.length > 64) {
      return '书籍名称不能超过64个字符';
    }
    return null;
  }

  static String? validatePrivacy(String? privacy) {
    const validPrivacy = ['public', 'private', 'vip'];
    if (privacy != null && !validPrivacy.contains(privacy)) {
      return '无效的隐私设置';
    }
    return null;
  }
}
```

### 卡片验证

```dart
class CardValidator {
  static String? validateQuestion(String? question) {
    if (question == null || question.trim().isEmpty) {
      return '问题内容不能为空';
    }
    return null;
  }

  static String? validateAnswer(String? answer) {
    if (answer == null || answer.trim().isEmpty) {
      return '答案内容不能为空';
    }
    return null;
  }
}
```

## 🔄 数据转换工具

### JSON 序列化工具

```dart
class ModelConverter {
  // 将模型转换为数据库格式
  static Map<String, dynamic> toDbMap(dynamic model) {
    final json = model.toJson();
    // 处理特殊字段
    if (json['created_at'] is DateTime) {
      json['created_at'] = (json['created_at'] as DateTime).toIso8601String();
    }
    if (json['updated_at'] is DateTime) {
      json['updated_at'] = (json['updated_at'] as DateTime).toIso8601String();
    }
    return json;
  }

  // 从数据库格式转换为模型
  static T fromDbMap<T>(Map<String, dynamic> map, T Function(Map<String, dynamic>) fromJson) {
    // 处理时间字段
    if (map['created_at'] is String) {
      map['created_at'] = DateTime.parse(map['created_at']);
    }
    if (map['updated_at'] is String) {
      map['updated_at'] = DateTime.parse(map['updated_at']);
    }
    return fromJson(map);
  }
}
```

## 📊 数据统计模型

### 书籍统计

```dart
class BookStats {
  int totalBooks;           // 总书籍数
  int totalCards;           // 总卡片数
  int studyingBooks;        // 学习中的书籍数
  int completedBooks;       // 已完成的书籍数
  DateTime? lastStudyTime;  // 最后学习时间

  BookStats({
    this.totalBooks = 0,
    this.totalCards = 0,
    this.studyingBooks = 0,
    this.completedBooks = 0,
    this.lastStudyTime,
  });
}
```

### 卡片统计

```dart
class CardStats {
  int newCards;             // 新卡片数
  int learningCards;        // 学习中卡片数
  int reviewCards;          // 复习卡片数
  int masteredCards;        // 已掌握卡片数
  double averageScore;      // 平均分数

  CardStats({
    this.newCards = 0,
    this.learningCards = 0,
    this.reviewCards = 0,
    this.masteredCards = 0,
    this.averageScore = 0.0,
  });
}
```

## 🔧 数据模型最佳实践

### 1. 命名规范
- 使用驼峰命名法
- 字段名要有明确含义
- 避免使用缩写

### 2. 类型安全
- 使用强类型定义
- 避免使用 dynamic 类型
- 提供默认值

### 3. 序列化处理
- 实现完整的 JSON 序列化
- 处理空值情况
- 支持嵌套对象

### 4. 数据验证
- 在模型层进行基础验证
- 提供友好的错误信息
- 支持自定义验证规则

### 5. 版本兼容
- 支持向后兼容
- 处理字段变更
- 提供数据迁移机制
