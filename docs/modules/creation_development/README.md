# Creation 模块开发文档

## 📖 模块概述

Creation 模块是 CheeStack 应用的核心功能模块，负责书籍和卡片的创建、编辑、管理以及本地优先的数据同步。该模块采用本地优先的设计理念，确保用户在离线状态下也能正常使用所有功能。

### 核心特性

- ✅ **本地优先存储** - 所有数据优先保存在本地 SQLite 数据库
- ✅ **离线完整功能** - 支持离线创建、编辑、删除书籍和卡片
- ✅ **智能数据同步** - 自动/手动同步本地数据到云端
- ✅ **冲突处理机制** - 本地优先的冲突解决策略
- ✅ **多媒体支持** - 支持图片、音频等多媒体资源
- ✅ **搜索和筛选** - 强大的本地搜索和筛选功能

### 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Layer      │    │  Controller     │    │  Service Layer  │
│                 │    │                 │    │                 │
│ - CreationPage  │◄──►│ CreationCtrl    │◄──►│ BookDataService │
│ - BookListPage  │    │ BookCtrl        │    │ CardDataService │
│ - BookEditPage  │    │ BookDetailCtrl  │    │ ApiSyncService  │
│ - CardListPage  │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   DAO Layer     │
                                               │                 │
                                               │ - BookDao       │
                                               │ - CardDao       │
                                               │ - BaseDao       │
                                               └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │ Local Database  │
                                               │                 │
                                               │ SQLite Database │
                                               │ - books         │
                                               │ - cards         │
                                               │ - sync_records  │
                                               └─────────────────┘
```

## 📁 目录结构

```
lib/features/creation/
├── apis/                    # API 服务层
│   ├── book_api.dart       # 书籍 API 接口
│   ├── book_service.dart   # 书籍网络服务
│   ├── card_api.dart       # 卡片 API 接口
│   └── card_service.dart   # 卡片网络服务
├── controllers/            # 控制器层
│   ├── creation_controller.dart      # 主创作控制器
│   ├── book_controller.dart          # 书籍控制器
│   └── book_detail_controller.dart   # 书籍详情控制器
├── pages/                  # 页面层
│   ├── creation_page.dart           # 创作主页面
│   ├── book_list_page.dart          # 书籍列表页面
│   ├── book_detail_page.dart        # 书籍详情页面
│   ├── book_edit_page.dart          # 书籍编辑页面
│   ├── card_list_page.dart          # 卡片列表页面
│   └── widgets/                     # 页面组件
│       ├── book_card.dart           # 书籍卡片组件
│       ├── book_filter_bar.dart     # 书籍筛选栏
│       ├── book_toolbar.dart        # 书籍工具栏
│       └── search_bar.dart          # 搜索栏组件
├── models/                 # 数据模型（空目录，使用全局模型）
├── index.dart             # 模块导出文件
└── readme.md              # 模块需求文档
```

## 🔗 相关文档

- [📋 开发总结](./SUMMARY.md) - **模块开发完整总结和技术架构概览**
- [界面设计文档](./ui_design.md) - 详细的界面设计和组件说明
- [数据模型文档](./data_models.md) - 数据结构和模型定义
- [API接口文档](./api_interfaces.md) - 后端接口和数据交互
- [本地存储文档](./local_storage.md) - 本地数据库设计和DAO层
- [数据同步文档](./data_sync.md) - 数据同步机制和冲突处理
- [控制器文档](./controllers.md) - 业务逻辑和状态管理
- [测试文档](./testing.md) - 测试策略和用例

## 📋 文档概览

| 文档                           | 内容概述                           | 适用人员                         |
| ------------------------------ | ---------------------------------- | -------------------------------- |
| [📋 开发总结](./SUMMARY.md)     | **模块完整总结、架构概览、技术栈** | **项目经理、技术负责人、新成员** |
| [界面设计](./ui_design.md)     | 页面结构、组件设计、交互规范       | UI/UX设计师、前端开发者          |
| [数据模型](./data_models.md)   | 数据结构、字段定义、验证规则       | 全栈开发者、数据库设计师         |
| [API接口](./api_interfaces.md) | 接口定义、请求响应格式、错误处理   | 前后端开发者、测试工程师         |
| [本地存储](./local_storage.md) | 数据库设计、DAO实现、性能优化      | 前端开发者、数据库工程师         |
| [数据同步](./data_sync.md)     | 同步策略、冲突处理、离线支持       | 全栈开发者、架构师               |
| [控制器](./controllers.md)     | 状态管理、业务逻辑、生命周期       | 前端开发者、架构师               |
| [测试](./testing.md)           | 测试策略、用例设计、自动化测试     | 测试工程师、开发者               |

## 🚀 快速开始

### 1. 模块初始化

```dart
// 在应用启动时初始化相关服务
await Get.putAsync(() => DatabaseService().init());
await Get.putAsync(() => BookDataService().init());
await Get.putAsync(() => CardDataService().init());
```

### 2. 基本使用

```dart
// 创建书籍
final bookDataService = BookDataService.to;
final book = await bookDataService.createBook(
  name: '我的新书',
  brief: '这是一本关于学习的书',
  privacy: 'private',
);

// 创建卡片
final cardDataService = CardDataService.to;
final card = await cardDataService.createCard(
  bookId: book.id!,
  title: '卡片标题',
  question: '这是问题',
  answer: '这是答案',
);
```

### 3. 页面导航

```dart
// 跳转到创作主页
Get.toNamed(AppRoutes.creation);

// 跳转到书籍列表
Get.toNamed(AppRoutes.bookList);

// 跳转到书籍编辑
Get.toNamed(AppRoutes.bookEditor, arguments: bookModel);
```

## 📋 开发规范

### 1. 数据流原则

- **本地优先**: 所有创建/编辑操作必须先写入本地数据库
- **异步同步**: 本地操作完成后，异步同步到云端
- **错误处理**: 网络错误不影响本地操作的成功

### 2. 代码结构

- **控制器职责**: 只负责 UI 状态管理和业务调度
- **服务层职责**: 处理具体的数据操作和业务逻辑
- **DAO层职责**: 负责数据库的增删改查操作

### 3. 错误处理

- 使用统一的错误处理机制
- 提供用户友好的错误提示
- 记录详细的错误日志用于调试

## 🔧 依赖关系

### 内部依赖

- `services/book_data_service.dart` - 书籍数据服务
- `services/card_data_service.dart` - 卡片数据服务
- `services/api_sync_service.dart` - 数据同步服务
- `services/dao/` - 数据访问对象层

### 外部依赖

- `GetX` - 状态管理和依赖注入
- `sqflite` - 本地数据库
- `dio` - 网络请求
- `flutter_screenutil` - 屏幕适配

## 📊 性能优化

### 1. 数据库优化

- 使用索引提高查询性能
- 分页加载大量数据
- 批量操作减少数据库访问

### 2. 内存管理

- 及时释放不需要的资源
- 使用对象池复用对象
- 避免内存泄漏

### 3. 网络优化

- 智能重试机制
- 请求去重和缓存
- 压缩传输数据

## 🐛 常见问题

### 1. 数据同步失败

**问题**: 本地数据无法同步到服务器
**解决**: 检查网络连接，查看同步日志，手动触发同步

### 2. 数据冲突

**问题**: 本地和服务器数据不一致
**解决**: 采用本地优先策略，提示用户手动处理冲突

### 3. 性能问题

**问题**: 大量数据时页面卡顿
**解决**: 实现分页加载，优化数据库查询，使用虚拟列表

## 📈 未来规划

- [ ] AI 辅助创作功能
- [ ] 多人协作编辑
- [ ] 版本历史管理
- [ ] 数据导入导出
- [ ] 高级搜索功能
