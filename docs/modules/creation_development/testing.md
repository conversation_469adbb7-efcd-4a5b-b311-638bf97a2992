# Creation 模块测试文档

## 🧪 测试策略概览

Creation 模块采用多层次的测试策略，确保代码质量和功能稳定性。测试覆盖单元测试、集成测试、UI测试和端到端测试。

```
┌─────────────────────────────────────────────────────────────┐
│                    Testing Pyramid                         │
├─────────────────────────────────────────────────────────────┤
│                  E2E Tests (少量)                           │
│              ┌─────────────────────┐                        │
│              │  完整用户流程测试    │                        │
├─────────────────────────────────────────────────────────────┤
│                Integration Tests (适量)                     │
│         ┌─────────────────────────────────┐                 │
│         │  服务层集成测试  │  API集成测试  │                 │
├─────────────────────────────────────────────────────────────┤
│                  Unit Tests (大量)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ DAO测试 │ 服务测试 │ 控制器测试 │ 工具类测试 │ 模型测试 │ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 测试环境配置

### 测试依赖

```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.2
  build_runner: ^2.4.7
  sqflite_common_ffi: ^2.3.0
  integration_test:
    sdk: flutter
  patrol: ^2.6.0
```

### 测试配置文件

```dart
// test/test_config.dart
class TestConfig {
  static const String testDatabaseName = 'test_cheestack.db';
  static const String testUserId = 'test_user_123';
  
  /// 初始化测试环境
  static Future<void> setupTestEnvironment() async {
    // 初始化 sqflite_ffi 用于桌面测试
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
    
    // 设置测试用的 GetX 绑定
    Get.testMode = true;
  }
  
  /// 清理测试环境
  static Future<void> tearDownTestEnvironment() async {
    await Get.reset();
  }
}
```

## 🧩 单元测试

### DAO 层测试

```dart
// test/dao/book_dao_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:cheestack_flt/services/dao/book_dao.dart';
import 'package:cheestack_flt/models/book.dart';

void main() {
  group('BookDao Tests', () {
    late Database database;
    late BookDao bookDao;
    
    setUpAll(() async {
      // 初始化测试数据库
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });
    
    setUp(() async {
      // 创建内存数据库
      database = await openDatabase(
        inMemoryDatabasePath,
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE books (
              id INTEGER PRIMARY KEY,
              user_id TEXT,
              name TEXT NOT NULL,
              brief TEXT,
              cover TEXT,
              privacy TEXT DEFAULT 'private',
              created_at TEXT,
              updated_at TEXT,
              synced_at TEXT,
              is_dirty INTEGER DEFAULT 0
            )
          ''');
        },
      );
      
      bookDao = BookDao();
      // 注入测试数据库
      bookDao.database = database;
    });
    
    tearDown(() async {
      await database.close();
    });
    
    test('should insert book successfully', () async {
      // Arrange
      final book = BookModel(
        name: 'Test Book',
        brief: 'Test Brief',
        privacy: 'private',
      );
      
      // Act
      final bookId = await bookDao.insertWithUserId(book, 'test_user');
      
      // Assert
      expect(bookId, greaterThan(0));
      
      final insertedBook = await bookDao.findById(bookId);
      expect(insertedBook, isNotNull);
      expect(insertedBook!.name, equals('Test Book'));
      expect(insertedBook.brief, equals('Test Brief'));
    });
    
    test('should find books by user id', () async {
      // Arrange
      await bookDao.insertWithUserId(
        BookModel(name: 'Book 1', privacy: 'private'),
        'user1',
      );
      await bookDao.insertWithUserId(
        BookModel(name: 'Book 2', privacy: 'private'),
        'user1',
      );
      await bookDao.insertWithUserId(
        BookModel(name: 'Book 3', privacy: 'private'),
        'user2',
      );
      
      // Act
      final user1Books = await bookDao.findByUserId('user1');
      final user2Books = await bookDao.findByUserId('user2');
      
      // Assert
      expect(user1Books.length, equals(2));
      expect(user2Books.length, equals(1));
      expect(user1Books.map((b) => b.name), containsAll(['Book 1', 'Book 2']));
    });
    
    test('should search books by name', () async {
      // Arrange
      await bookDao.insertWithUserId(
        BookModel(name: 'English Vocabulary', privacy: 'private'),
        'user1',
      );
      await bookDao.insertWithUserId(
        BookModel(name: 'Chinese Grammar', privacy: 'private'),
        'user1',
      );
      await bookDao.insertWithUserId(
        BookModel(name: 'Math Formulas', privacy: 'private'),
        'user1',
      );
      
      // Act
      final englishBooks = await bookDao.searchByName('user1', 'English');
      final grammarBooks = await bookDao.searchByName('user1', 'Grammar');
      final noResults = await bookDao.searchByName('user1', 'Physics');
      
      // Assert
      expect(englishBooks.length, equals(1));
      expect(englishBooks.first.name, equals('English Vocabulary'));
      expect(grammarBooks.length, equals(1));
      expect(noResults.length, equals(0));
    });
    
    test('should check if book name exists', () async {
      // Arrange
      await bookDao.insertWithUserId(
        BookModel(name: 'Existing Book', privacy: 'private'),
        'user1',
      );
      
      // Act & Assert
      expect(await bookDao.isNameExists('user1', 'Existing Book'), isTrue);
      expect(await bookDao.isNameExists('user1', 'Non-existing Book'), isFalse);
      expect(await bookDao.isNameExists('user2', 'Existing Book'), isFalse);
    });
    
    test('should update book successfully', () async {
      // Arrange
      final bookId = await bookDao.insertWithUserId(
        BookModel(name: 'Original Name', brief: 'Original Brief'),
        'user1',
      );
      
      // Act
      final success = await bookDao.updateBook(
        bookId,
        name: 'Updated Name',
        brief: 'Updated Brief',
      );
      
      // Assert
      expect(success, isTrue);
      
      final updatedBook = await bookDao.findById(bookId);
      expect(updatedBook!.name, equals('Updated Name'));
      expect(updatedBook.brief, equals('Updated Brief'));
    });
  });
}
```

### 服务层测试

```dart
// test/services/book_data_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cheestack_flt/services/book_data_service.dart';
import 'package:cheestack_flt/services/dao/book_dao.dart';
import 'package:cheestack_flt/services/user_data_service.dart';

// 生成 Mock 类
@GenerateMocks([BookDao, UserDataService])
import 'book_data_service_test.mocks.dart';

void main() {
  group('BookDataService Tests', () {
    late BookDataService bookDataService;
    late MockBookDao mockBookDao;
    late MockUserDataService mockUserDataService;
    
    setUp(() {
      mockBookDao = MockBookDao();
      mockUserDataService = MockUserDataService();
      
      // 创建服务实例并注入 mock 依赖
      bookDataService = BookDataService();
      bookDataService.bookDao = mockBookDao;
      bookDataService.userDataService = mockUserDataService;
    });
    
    test('should create book successfully', () async {
      // Arrange
      const userId = 'test_user';
      const bookName = 'Test Book';
      const bookBrief = 'Test Brief';
      
      when(mockUserDataService.currentUser)
          .thenReturn(UserModel(id: userId));
      when(mockBookDao.isNameExists(userId, bookName))
          .thenAnswer((_) async => false);
      when(mockBookDao.insertWithUserId(any, userId))
          .thenAnswer((_) async => 1);
      
      // Act
      final result = await bookDataService.createBook(
        name: bookName,
        brief: bookBrief,
      );
      
      // Assert
      expect(result, isNotNull);
      expect(result!.name, equals(bookName));
      expect(result.brief, equals(bookBrief));
      
      verify(mockBookDao.isNameExists(userId, bookName)).called(1);
      verify(mockBookDao.insertWithUserId(any, userId)).called(1);
    });
    
    test('should throw exception when book name already exists', () async {
      // Arrange
      const userId = 'test_user';
      const bookName = 'Existing Book';
      
      when(mockUserDataService.currentUser)
          .thenReturn(UserModel(id: userId));
      when(mockBookDao.isNameExists(userId, bookName))
          .thenAnswer((_) async => true);
      
      // Act & Assert
      expect(
        () => bookDataService.createBook(name: bookName),
        throwsA(isA<Exception>()),
      );
      
      verify(mockBookDao.isNameExists(userId, bookName)).called(1);
      verifyNever(mockBookDao.insertWithUserId(any, any));
    });
    
    test('should return null when user is not logged in', () async {
      // Arrange
      when(mockUserDataService.currentUser).thenReturn(null);
      
      // Act
      final result = await bookDataService.createBook(name: 'Test Book');
      
      // Assert
      expect(result, isNull);
      verifyNever(mockBookDao.isNameExists(any, any));
      verifyNever(mockBookDao.insertWithUserId(any, any));
    });
  });
}
```

### 控制器测试

```dart
// test/controllers/creation_controller_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/services/book_data_service.dart';
import 'package:cheestack_flt/services/card_data_service.dart';

@GenerateMocks([BookDataService, CardDataService])
import 'creation_controller_test.mocks.dart';

void main() {
  group('CreationController Tests', () {
    late CreationController controller;
    late MockBookDataService mockBookDataService;
    late MockCardDataService mockCardDataService;
    
    setUp(() {
      // 设置 GetX 测试模式
      Get.testMode = true;
      
      mockBookDataService = MockBookDataService();
      mockCardDataService = MockCardDataService();
      
      // 注册 mock 服务
      Get.put<BookDataService>(mockBookDataService);
      Get.put<CardDataService>(mockCardDataService);
      
      controller = CreationController();
    });
    
    tearDown(() {
      Get.reset();
    });
    
    test('should load creation stats successfully', () async {
      // Arrange
      when(mockBookDataService.getBookStats())
          .thenAnswer((_) async => {'total_books': 5});
      when(mockCardDataService.getCardStats())
          .thenAnswer((_) async => {'total_cards': 50});
      
      // Act
      await controller.loadCreationStats();
      
      // Assert
      expect(controller.totalBooks, equals(5));
      expect(controller.totalCards, equals(50));
      expect(controller.isLoading, isFalse);
      
      verify(mockBookDataService.getBookStats()).called(1);
      verify(mockCardDataService.getCardStats()).called(1);
    });
    
    test('should handle error when loading stats fails', () async {
      // Arrange
      when(mockBookDataService.getBookStats())
          .thenThrow(Exception('Network error'));
      when(mockCardDataService.getCardStats())
          .thenAnswer((_) async => {'total_cards': 0});
      
      // Act
      await controller.loadCreationStats();
      
      // Assert
      expect(controller.isLoading, isFalse);
      // 错误处理后，统计数据应该保持默认值或之前的值
      expect(controller.totalBooks, equals(0));
    });
    
    test('should filter book list correctly', () async {
      // Arrange
      final books = [
        BookModel(name: 'English Book', privacy: 'public'),
        BookModel(name: 'Chinese Book', privacy: 'private'),
        BookModel(name: 'Math Book', privacy: 'public'),
      ];
      
      when(mockBookDataService.getUserBooks(orderBy: anyNamed('orderBy')))
          .thenAnswer((_) async => books);
      
      controller.bookList = books;
      
      // Act
      controller._applyFiltersAndSort();
      
      // Assert
      expect(controller.filteredBookList.length, equals(3));
      expect(controller.filteredBookList.map((b) => b.name),
          containsAll(['English Book', 'Chinese Book', 'Math Book']));
    });
    
    test('should search books by keyword', () async {
      // Arrange
      final searchResults = [
        BookModel(name: 'English Vocabulary'),
        BookModel(name: 'English Grammar'),
      ];
      
      when(mockBookDataService.searchBooks('English'))
          .thenAnswer((_) async => searchResults);
      
      // Act
      controller.onBookSearchChanged('English');
      
      // Assert
      verify(mockBookDataService.searchBooks('English')).called(1);
      expect(controller.bookSearchKeyword, equals('English'));
    });
  });
}
```

## 🔗 集成测试

### API 集成测试

```dart
// test/integration/api_integration_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/features/creation/apis/book_service.dart';
import 'package:cheestack_flt/models/book.dart';

void main() {
  group('Book API Integration Tests', () {
    late BookService bookService;
    
    setUpAll(() {
      // 设置测试环境，使用测试服务器
      bookService = BookService();
      // 配置测试 API 端点
    });
    
    test('should create and retrieve book via API', () async {
      // Arrange
      final bookData = {
        'name': 'Integration Test Book',
        'brief': 'Created by integration test',
        'privacy': 'private',
      };
      
      // Act - 创建书籍
      final createdBook = await bookService.createBook(bookData, null);
      
      // Assert - 验证创建结果
      expect(createdBook.name, equals('Integration Test Book'));
      expect(createdBook.id, isNotNull);
      
      // Act - 获取书籍详情
      final retrievedBook = await bookService.getBookDetail(createdBook.id!);
      
      // Assert - 验证获取结果
      expect(retrievedBook.name, equals(createdBook.name));
      expect(retrievedBook.brief, equals(createdBook.brief));
      
      // Cleanup - 删除测试数据
      await bookService.deleteBook(createdBook.id!);
    });
    
    test('should handle API errors gracefully', () async {
      // Act & Assert
      expect(
        () => bookService.getBookDetail(-1), // 无效ID
        throwsA(isA<Exception>()),
      );
    });
  });
}
```

### 数据库集成测试

```dart
// test/integration/database_integration_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/services/database_service.dart';
import 'package:cheestack_flt/services/book_data_service.dart';
import 'package:cheestack_flt/services/card_data_service.dart';

void main() {
  group('Database Integration Tests', () {
    late DatabaseService databaseService;
    late BookDataService bookDataService;
    late CardDataService cardDataService;
    
    setUpAll(() async {
      // 初始化测试数据库
      databaseService = DatabaseService();
      await databaseService.init();
      
      bookDataService = BookDataService();
      cardDataService = CardDataService();
      
      await bookDataService.init();
      await cardDataService.init();
    });
    
    tearDownAll(() async {
      // 清理测试数据库
      await databaseService.database.close();
    });
    
    test('should create book and associated cards', () async {
      // Arrange
      const userId = 'test_user';
      
      // Act - 创建书籍
      final book = await bookDataService.createBook(
        name: 'Test Book with Cards',
        brief: 'Integration test book',
      );
      
      expect(book, isNotNull);
      
      // Act - 为书籍创建卡片
      final card = await cardDataService.createCard(
        bookId: book!.id!,
        title: 'Test Card',
        question: 'What is integration testing?',
        answer: 'Testing multiple components together',
      );
      
      expect(card, isNotNull);
      
      // Assert - 验证关联关系
      final bookCards = await cardDataService.getBookCards(book.id!);
      expect(bookCards.length, equals(1));
      expect(bookCards.first.title, equals('Test Card'));
    });
  });
}
```

## 🖥️ UI 测试

### Widget 测试

```dart
// test/widgets/book_card_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/features/creation/pages/widgets/book_card.dart';
import 'package:cheestack_flt/models/book.dart';

void main() {
  group('BookCard Widget Tests', () {
    testWidgets('should display book information correctly', (tester) async {
      // Arrange
      final book = BookModel(
        id: 1,
        name: 'Test Book',
        brief: 'Test Brief',
        privacy: 'private',
        createdAt: '2024-01-01T00:00:00Z',
      );
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BookListCard(
              book: book,
              controller: null, // 在实际测试中需要提供 mock controller
            ),
          ),
        ),
      );
      
      // Assert
      expect(find.text('Test Book'), findsOneWidget);
      expect(find.text('Test Brief'), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget); // 私有书籍图标
    });
    
    testWidgets('should handle tap events', (tester) async {
      // Arrange
      bool tapped = false;
      final book = BookModel(name: 'Tappable Book');
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GestureDetector(
              onTap: () => tapped = true,
              child: BookListCard(book: book, controller: null),
            ),
          ),
        ),
      );
      
      // Act
      await tester.tap(find.byType(BookListCard));
      await tester.pump();
      
      // Assert
      expect(tapped, isTrue);
    });
  });
}
```

## 🚀 端到端测试

### 完整用户流程测试

```dart
// integration_test/creation_flow_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:cheestack_flt/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Creation Flow E2E Tests', () {
    testWidgets('complete book creation and management flow', (tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      
      // 1. 导航到创作页面
      await tester.tap(find.byIcon(Icons.create));
      await tester.pumpAndSettle();
      
      // 2. 点击书籍管理
      await tester.tap(find.text('书籍管理'));
      await tester.pumpAndSettle();
      
      // 3. 创建新书籍
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();
      
      // 4. 填写书籍信息
      await tester.enterText(find.byType(TextField).first, 'E2E Test Book');
      await tester.enterText(find.byType(TextField).at(1), 'Created by E2E test');
      
      // 5. 保存书籍
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();
      
      // 6. 验证书籍创建成功
      expect(find.text('E2E Test Book'), findsOneWidget);
      
      // 7. 点击进入书籍详情
      await tester.tap(find.text('E2E Test Book'));
      await tester.pumpAndSettle();
      
      // 8. 验证书籍详情页面
      expect(find.text('书籍详情'), findsOneWidget);
      expect(find.text('E2E Test Book'), findsOneWidget);
      
      // 9. 编辑书籍
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pumpAndSettle();
      
      // 10. 修改书籍名称
      await tester.enterText(find.byType(TextField).first, 'Updated E2E Test Book');
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();
      
      // 11. 验证修改成功
      expect(find.text('Updated E2E Test Book'), findsOneWidget);
    });
    
    testWidgets('search and filter functionality', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 导航到书籍列表
      await tester.tap(find.byIcon(Icons.create));
      await tester.pumpAndSettle();
      await tester.tap(find.text('书籍管理'));
      await tester.pumpAndSettle();
      
      // 测试搜索功能
      await tester.enterText(find.byType(TextField), 'Test');
      await tester.pumpAndSettle(Duration(seconds: 1));
      
      // 验证搜索结果
      expect(find.textContaining('Test'), findsAtLeastNWidgets(1));
      
      // 清空搜索
      await tester.enterText(find.byType(TextField), '');
      await tester.pumpAndSettle();
      
      // 测试筛选功能
      await tester.tap(find.byIcon(Icons.filter_list));
      await tester.pumpAndSettle();
      
      // 选择筛选条件
      await tester.tap(find.text('私有'));
      await tester.tap(find.text('确定'));
      await tester.pumpAndSettle();
      
      // 验证筛选结果
      // 这里需要根据实际的筛选逻辑进行验证
    });
  });
}
```

## 📊 测试覆盖率

### 生成覆盖率报告

```bash
# 运行测试并生成覆盖率报告
flutter test --coverage

# 生成 HTML 格式的覆盖率报告
genhtml coverage/lcov.info -o coverage/html

# 查看覆盖率报告
open coverage/html/index.html
```

### 覆盖率目标

- **整体覆盖率**: ≥ 80%
- **核心业务逻辑**: ≥ 90%
- **DAO 层**: ≥ 95%
- **服务层**: ≥ 85%
- **控制器层**: ≥ 80%

## 🔧 测试工具和辅助函数

### 测试数据工厂

```dart
// test/helpers/test_data_factory.dart
class TestDataFactory {
  static BookModel createTestBook({
    int? id,
    String? name,
    String? brief,
    String? privacy,
  }) {
    return BookModel(
      id: id ?? 1,
      name: name ?? 'Test Book',
      brief: brief ?? 'Test Brief',
      privacy: privacy ?? 'private',
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
    );
  }
  
  static CardModel createTestCard({
    int? id,
    String? title,
    String? question,
    String? answer,
  }) {
    return CardModel(
      id: id ?? 1,
      title: title ?? 'Test Card',
      question: question ?? 'Test Question',
      answer: answer ?? 'Test Answer',
      type: 'basic',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
  
  static List<BookModel> createTestBookList(int count) {
    return List.generate(
      count,
      (index) => createTestBook(
        id: index + 1,
        name: 'Test Book ${index + 1}',
      ),
    );
  }
}
```

### 测试辅助工具

```dart
// test/helpers/test_helpers.dart
class TestHelpers {
  /// 等待异步操作完成
  static Future<void> waitForAsync() async {
    await Future.delayed(Duration(milliseconds: 100));
  }
  
  /// 模拟网络延迟
  static Future<void> simulateNetworkDelay() async {
    await Future.delayed(Duration(milliseconds: 500));
  }
  
  /// 创建测试用的 GetX 绑定
  static void setupTestBindings() {
    Get.testMode = true;
    
    // 注册测试用的服务
    Get.put<DatabaseService>(MockDatabaseService());
    Get.put<BookDataService>(MockBookDataService());
    Get.put<CardDataService>(MockCardDataService());
  }
  
  /// 清理测试环境
  static void tearDownTest() {
    Get.reset();
  }
}
```

## 🚨 测试最佳实践

### 1. 测试命名规范

```dart
// 好的测试命名
test('should create book successfully when valid data provided', () async {
  // 测试实现
});

// 不好的测试命名
test('test create book', () async {
  // 测试实现
});
```

### 2. AAA 模式

```dart
test('should return filtered books when search keyword provided', () async {
  // Arrange - 准备测试数据和环境
  final books = TestDataFactory.createTestBookList(5);
  when(mockBookService.searchBooks('test')).thenAnswer((_) async => books);
  
  // Act - 执行被测试的操作
  final result = await bookDataService.searchBooks('test');
  
  // Assert - 验证结果
  expect(result.length, equals(5));
  verify(mockBookService.searchBooks('test')).called(1);
});
```

### 3. 测试隔离

```dart
group('BookDataService Tests', () {
  setUp(() {
    // 每个测试前的准备工作
    TestHelpers.setupTestBindings();
  });
  
  tearDown(() {
    // 每个测试后的清理工作
    TestHelpers.tearDownTest();
  });
  
  // 各个测试用例...
});
```

### 4. 异常测试

```dart
test('should throw exception when network error occurs', () async {
  // Arrange
  when(mockApiService.createBook(any))
      .thenThrow(NetworkException('Connection failed'));
  
  // Act & Assert
  expect(
    () => bookDataService.createBook(name: 'Test'),
    throwsA(isA<NetworkException>()),
  );
});
```

## 📈 持续集成

### GitHub Actions 配置

```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Run tests
      run: flutter test --coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
```

通过完善的测试策略和持续集成，确保 Creation 模块的代码质量和功能稳定性。
