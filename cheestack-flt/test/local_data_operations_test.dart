import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/features/creation/apis/card_service.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';

import 'local_data_operations_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<BookDataService>(),
  MockSpec<CardDataService>(),
])
void main() {
  group('本地数据操作集成测试', () {
    late MockBookDataService mockBookDataService;
    late MockCardDataService mockCardDataService;

    setUp(() {
      Get.testMode = true;
      Get.reset();
      
      mockBookDataService = MockBookDataService();
      mockCardDataService = MockCardDataService();
    });

    tearDown(() {
      Get.reset();
    });

    group('CreationController 本地数据操作', () {
      test('应该优先使用本地数据服务加载书籍列表', () async {
        // Arrange
        Get.put<BookDataService>(mockBookDataService);
        final controller = CreationController();
        
        final expectedBooks = [
          BookModel(
            id: 1,
            name: '测试书籍1',
            brief: '简介1',
            privacy: 'private',
            createdAt: DateTime.now().toIso8601String(),
            updatedAt: DateTime.now().toIso8601String(),
          ),
          BookModel(
            id: 2,
            name: '测试书籍2',
            brief: '简介2',
            privacy: 'public',
            createdAt: DateTime.now().toIso8601String(),
            updatedAt: DateTime.now().toIso8601String(),
          ),
        ];

        when(mockBookDataService.getUserBooks(orderBy: anyNamed('orderBy')))
            .thenAnswer((_) async => expectedBooks);

        // Act
        await controller.loadBookList();

        // Assert
        expect(controller.bookList, equals(expectedBooks));
        verify(mockBookDataService.getUserBooks(orderBy: 'created_at DESC')).called(1);
      });

      test('应该支持搜索功能', () async {
        // Arrange
        Get.put<BookDataService>(mockBookDataService);
        final controller = CreationController();
        
        final keyword = '测试';
        final expectedBooks = [
          BookModel(
            id: 1,
            name: '测试书籍',
            brief: '测试简介',
            privacy: 'private',
            createdAt: DateTime.now().toIso8601String(),
            updatedAt: DateTime.now().toIso8601String(),
          ),
        ];

        controller.bookSearchKeyword = keyword;
        when(mockBookDataService.searchBooks(keyword))
            .thenAnswer((_) async => expectedBooks);

        // Act
        await controller.loadBookList();

        // Assert
        expect(controller.bookList, equals(expectedBooks));
        verify(mockBookDataService.searchBooks(keyword)).called(1);
      });

      test('当BookDataService未注册时应该有回退机制', () async {
        // Arrange - 不注册BookDataService
        final controller = CreationController();

        // Act
        await controller.loadBookList();

        // Assert - 应该不会崩溃，会回退到网络服务
        expect(controller.bookList, isNotNull);
        verifyNever(mockBookDataService.getUserBooks(orderBy: anyNamed('orderBy')));
      });
    });

    group('CardService 本地数据操作', () {
      test('应该优先使用本地数据服务获取卡片', () async {
        // Arrange
        Get.put<CardDataService>(mockCardDataService);
        final cardService = CardService();
        
        final bookId = 1;
        final expectedCards = [
          CardModel(
            id: 1,
            title: '测试卡片1',
            question: '问题1',
            answer: '答案1',
            type: 'basic',
            typeVersion: 1,
          ),
          CardModel(
            id: 2,
            title: '测试卡片2',
            question: '问题2',
            answer: '答案2',
            type: 'basic',
            typeVersion: 1,
          ),
        ];

        when(mockCardDataService.getBookCards(
          bookId,
          orderBy: anyNamed('orderBy'),
          limit: anyNamed('limit'),
          offset: anyNamed('offset'),
        )).thenAnswer((_) async => expectedCards);

        // Act
        final result = await cardService.getCardList(bookId: bookId);

        // Assert
        expect(result, equals(expectedCards));
        verify(mockCardDataService.getBookCards(
          bookId,
          orderBy: 'created_at DESC',
          limit: 20,
          offset: 0,
        )).called(1);
      });

      test('应该支持卡片搜索功能', () async {
        // Arrange
        Get.put<CardDataService>(mockCardDataService);
        final cardService = CardService();
        
        final keyword = '测试';
        final bookId = 1;
        final expectedCards = [
          CardModel(
            id: 1,
            title: '测试卡片',
            question: '测试问题',
            answer: '测试答案',
            type: 'basic',
            typeVersion: 1,
          ),
        ];

        when(mockCardDataService.searchCards(
          keyword,
          bookId: bookId,
          limit: anyNamed('limit'),
          offset: anyNamed('offset'),
        )).thenAnswer((_) async => expectedCards);

        // Act
        final result = await cardService.getCardList(
          search: keyword,
          bookId: bookId,
        );

        // Assert
        expect(result, equals(expectedCards));
        verify(mockCardDataService.searchCards(
          keyword,
          bookId: bookId,
          limit: 20,
          offset: 0,
        )).called(1);
      });

      test('当CardDataService未注册时应该返回空列表', () async {
        // Arrange - 不注册CardDataService
        final cardService = CardService();

        // Act
        final result = await cardService.getCardList();

        // Assert
        expect(result, isEmpty);
        verifyNever(mockCardDataService.getBookCards(any));
      });
    });

    group('数据操作一致性测试', () {
      test('本地数据服务应该保持数据一致性', () async {
        // Arrange
        Get.put<BookDataService>(mockBookDataService);
        Get.put<CardDataService>(mockCardDataService);
        
        final controller = CreationController();
        final cardService = CardService();
        
        final bookId = 1;
        final book = BookModel(
          id: bookId,
          name: '测试书籍',
          brief: '测试简介',
          privacy: 'private',
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
        );
        
        final cards = [
          CardModel(
            id: 1,
            title: '卡片1',
            question: '问题1',
            answer: '答案1',
            type: 'basic',
            typeVersion: 1,
          ),
        ];

        when(mockBookDataService.getUserBooks(orderBy: anyNamed('orderBy')))
            .thenAnswer((_) async => [book]);
        when(mockCardDataService.getBookCards(
          bookId,
          orderBy: anyNamed('orderBy'),
          limit: anyNamed('limit'),
          offset: anyNamed('offset'),
        )).thenAnswer((_) async => cards);

        // Act
        await controller.loadBookList();
        final cardResult = await cardService.getCardList(bookId: bookId);

        // Assert
        expect(controller.bookList, contains(book));
        expect(cardResult, equals(cards));
        
        // 验证数据一致性
        final bookFromController = controller.bookList.firstWhere((b) => b.id == bookId);
        expect(bookFromController.id, equals(bookId));
      });
    });
  });
}
