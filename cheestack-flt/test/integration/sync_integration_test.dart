import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';

void main() {
  group('同步功能集成测试', () {
    late ApiSyncService apiSyncService;

    setUp(() {
      Get.testMode = true;
      // 初始化服务
      apiSyncService = ApiSyncService();
    });

    tearDown(() {
      Get.reset();
    });

    group('API路径测试', () {
      test('应该使用正确的书籍API路径', () {
        // 验证API路径不包含重复的/api
        const expectedPath = '/api/v1/books';
        expect(expectedPath, isNot(contains('/api/api')));
        expect(expectedPath, startsWith('/api/v1/'));
      });

      test('应该使用正确的卡片API路径', () {
        const expectedPath = '/api/v1/cards';
        expect(expectedPath, isNot(contains('/api/api')));
        expect(expectedPath, startsWith('/api/v1/'));
      });

      test('应该使用正确的资源API路径', () {
        const expectedPath = '/api/v1/assets';
        expect(expectedPath, isNot(contains('/api/api')));
        expect(expectedPath, startsWith('/api/v1/'));
      });

      test('应该使用正确的学习记录API路径', () {
        const expectedPath = '/api/v1/study/my_reviews';
        expect(expectedPath, isNot(contains('/api/api')));
        expect(expectedPath, startsWith('/api/v1/'));
      });

      test('应该使用正确的书籍计划API路径', () {
        const expectedPath = '/api/v1/book_schedules';
        expect(expectedPath, isNot(contains('/api/api')));
        expect(expectedPath, startsWith('/api/v1/'));
      });
    });

    group('数据模型安全性测试', () {
      test('BookModel应该安全处理null值', () {
        final jsonData = {
          'id': 1,
          'name': null,
          'brief': null,
          'cover': null,
          'privacy': null,
          'created_at': null,
          'updated_at': null,
          'user': null,
        };

        expect(() => BookModel.fromJson(jsonData), returnsNormally);

        final book = BookModel.fromJson(jsonData);
        expect(book.id, equals(1));
        expect(book.name, isNull);
        expect(book.brief, isNull);
        expect(book.cover, isNull);
        expect(book.privacy, isNull);
        expect(book.createdAt, isNull);
        expect(book.updatedAt, isNull);
        expect(book.user, isNull);
      });

      test('CardModel应该安全处理null值', () {
        final jsonData = {
          'id': 1,
          'type': null,
          'type_version': null,
          'title': null,
          'question': null,
          'answer': null,
          'extra': null,
          'schedule_id': null,
          'created_at': null,
          'updated_at': null,
          'user': null,
          'card_assets': null,
        };

        expect(() => CardModel.fromJson(jsonData), returnsNormally);

        final card = CardModel.fromJson(jsonData);
        expect(card.id, equals(1));
        expect(card.type, isNull);
        expect(card.typeVersion, equals(1)); // 默认值
        expect(card.title, isNull);
        expect(card.question, isNull);
        expect(card.answer, isNull);
        expect(card.extra, isNull);
        expect(card.scheduleId, isNull);
        expect(card.createdAt, isNull);
        expect(card.updatedAt, isNull);
        expect(card.user, isNull);
        expect(card.cardAssets, isNull);
      });

      test('UserModel应该安全处理null值', () {
        final jsonData = {
          'id': null,
          'username': null,
          'mobile': null,
          'email': null,
          'avatar': null,
          'intro': null,
          'created_at': null,
          'updated_at': null,
          'config': null,
          'token': null,
        };

        expect(() => UserModel.fromJson(jsonData), returnsNormally);

        final user = UserModel.fromJson(jsonData);
        expect(user.id, isNull);
        expect(user.username, isNull);
        expect(user.mobile, isNull);
        expect(user.email, isNull);
        expect(user.avatar, isNull);
        expect(user.intro, isNull);
        expect(user.createdAt, isNull);
        expect(user.updatedAt, isNull);
        expect(user.config, isNull);
        expect(user.token, isNull);
      });
    });

    group('类型转换安全性测试', () {
      test('应该安全处理非字符串类型的数据', () {
        final jsonData = {
          'id': 1,
          'name': 123, // 数字
          'brief': true, // 布尔值
          'cover': ['array'], // 数组
          'privacy': {'object': 'value'}, // 对象
          'created_at': 1640995200000, // 时间戳
          'updated_at': '2024-01-01T12:00:00Z', // 正常字符串
          'user': null,
        };

        expect(() => BookModel.fromJson(jsonData), returnsNormally);

        final book = BookModel.fromJson(jsonData);
        expect(book.id, equals(1));
        expect(book.name, equals('123'));
        expect(book.brief, equals('true'));
        expect(book.cover, equals('[array]'));
        expect(book.privacy, equals('{object: value}'));
        expect(book.createdAt, equals('1640995200000'));
        expect(book.updatedAt, equals('2024-01-01T12:00:00Z'));
      });

      test('应该安全处理DateTime字段', () {
        final jsonData = {
          'id': 1,
          'type': 'general',
          'type_version': 1,
          'title': '测试卡片',
          'question': '问题',
          'answer': '答案',
          'extra': null,
          'schedule_id': null,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': 1640995200000, // 时间戳数字
          'user': null,
          'card_assets': null,
        };

        expect(() => CardModel.fromJson(jsonData), returnsNormally);

        final card = CardModel.fromJson(jsonData);
        expect(card.createdAt, isA<DateTime>());
        expect(card.updatedAt, isA<DateTime>());
      });
    });

    group('错误恢复测试', () {
      test('应该正确处理空的用户ID', () async {
        final result = await apiSyncService.syncAllData(userId: '');
        expect(result, isFalse);
      });

      test('应该正确处理无效的JSON数据', () {
        final invalidJsonData = <String, Object?>{};

        expect(() => BookModel.fromJson(invalidJsonData), returnsNormally);
        expect(() => CardModel.fromJson(invalidJsonData), returnsNormally);
        expect(() => UserModel.fromJson(invalidJsonData), returnsNormally);
      });

      test('应该正确处理包含意外字段的数据', () {
        final dataWithExtraFields = {
          'id': 1,
          'name': '测试书籍',
          'unexpected_field': 'unexpected_value',
          'another_field': {'nested': 'object'},
          'array_field': [1, 2, 3],
          'null_field': null,
        };

        expect(() => BookModel.fromJson(dataWithExtraFields), returnsNormally);

        final book = BookModel.fromJson(dataWithExtraFields);
        expect(book.id, equals(1));
        expect(book.name, equals('测试书籍'));
      });
    });

    group('URL构建测试', () {
      test('baseUrl和路径组合应该正确', () {
        const baseUrl = 'http://192.168.10.80:9000/api';
        const path = '/v1/books';
        final fullUrl = '$baseUrl$path';

        expect(fullUrl, equals('http://192.168.10.80:9000/api/v1/books'));
        expect(fullUrl, isNot(contains('/api/api')));
      });

      test('不同API端点的URL应该正确', () {
        const baseUrl = 'http://192.168.10.80:9000/api';

        final endpoints = [
          '/v1/books',
          '/v1/cards',
          '/v1/assets',
          '/v1/study/my_reviews',
          '/v1/book_schedules',
          '/v1/study/records',
        ];

        for (final endpoint in endpoints) {
          final fullUrl = '$baseUrl$endpoint';
          expect(fullUrl, isNot(contains('/api/api')));
          expect(fullUrl, startsWith('http://192.168.10.80:9000/api/v1/'));
        }
      });
    });
  });
}
