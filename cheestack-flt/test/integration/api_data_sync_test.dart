import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/models/index.dart';

void main() {
  group('前后端数据同步测试', () {
    group('BookModel 数据同步', () {
      test('应该正确处理后端返回的完整书籍数据', () {
        // 模拟后端返回的数据格式
        final backendData = {
          'id': 1,
          'name': '测试书籍',
          'brief': '这是一个测试书籍',
          'cover': 'https://example.com/cover.jpg',
          'privacy': 'free',
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T12:00:00Z',
          'user': {
            'id': 'user123',
            'username': 'testuser',
            'mobile': '13800138000',
            'email': '<EMAIL>',
            'avatar': 'https://example.com/avatar.jpg',
            'intro': '测试用户',
            'created_at': '2023-12-01T00:00:00Z',
            'updated_at': '2023-12-01T12:00:00Z',
          }
        };

        final book = BookModel.fromJson(backendData);

        expect(book.id, equals(1));
        expect(book.name, equals('测试书籍'));
        expect(book.brief, equals('这是一个测试书籍'));
        expect(book.cover, equals('https://example.com/cover.jpg'));
        expect(book.privacy, equals('free'));
        expect(book.createdAt, equals('2024-01-01T00:00:00Z'));
        expect(book.updatedAt, equals('2024-01-01T12:00:00Z'));
        expect(book.user, isNotNull);
        expect(book.user!.id, equals('user123'));
        expect(book.user!.username, equals('testuser'));
      });

      test('应该正确处理后端返回的null字段', () {
        final backendData = {
          'id': 1,
          'name': null,
          'brief': null,
          'cover': null,
          'privacy': null,
          'created_at': null,
          'updated_at': null,
          'user': null,
        };

        final book = BookModel.fromJson(backendData);

        expect(book.id, equals(1));
        expect(book.name, isNull);
        expect(book.brief, isNull);
        expect(book.cover, isNull);
        expect(book.privacy, isNull);
        expect(book.createdAt, isNull);
        expect(book.updatedAt, isNull);
        expect(book.user, isNull);
      });

      test('应该正确处理后端返回的混合数据类型', () {
        final backendData = {
          'id': 1,
          'name': 123, // 数字类型
          'brief': true, // 布尔类型
          'cover': ['array'], // 数组类型
          'privacy': {'object': 'value'}, // 对象类型
          'created_at': 1640995200000, // 时间戳
          'updated_at': '2024-01-01T12:00:00Z', // 字符串
          'user': null,
        };

        final book = BookModel.fromJson(backendData);

        expect(book.id, equals(1));
        expect(book.name, equals('123'));
        expect(book.brief, equals('true'));
        expect(book.cover, equals('[array]'));
        expect(book.privacy, equals('{object: value}'));
        expect(book.createdAt, equals('1640995200000'));
        expect(book.updatedAt, equals('2024-01-01T12:00:00Z'));
        expect(book.user, isNull);
      });
    });

    group('CardModel 数据同步', () {
      test('应该正确处理后端返回的完整卡片数据', () {
        final backendData = {
          'id': 1,
          'type': 'general',
          'type_version': 1,
          'title': '测试卡片',
          'question': '这是问题',
          'answer': '这是答案',
          'extra': {'key': 'value'},
          'schedule_id': 123,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T12:00:00Z',
          'user': {
            'id': 'user123',
            'username': 'testuser',
            'created_at': '2023-12-01T00:00:00Z',
            'updated_at': '2023-12-01T12:00:00Z',
          },
          'card_assets': [
            {
              'id': 1,
              'card_id': 1,
              'asset_id': 1,
              'is_correct': true,
              'type': 'image',
              'text': '图片描述',
              'url': 'https://example.com/image.jpg',
              'name': '测试图片',
            }
          ]
        };

        final card = CardModel.fromJson(backendData);

        expect(card.id, equals(1));
        expect(card.type, equals('general'));
        expect(card.typeVersion, equals(1));
        expect(card.title, equals('测试卡片'));
        expect(card.question, equals('这是问题'));
        expect(card.answer, equals('这是答案'));
        expect(card.extra, equals({'key': 'value'}));
        expect(card.scheduleId, equals(123));
        expect(card.createdAt, isNotNull);
        expect(card.updatedAt, isNotNull);
        expect(card.user, isNotNull);
        expect(card.cardAssets, isNotNull);
        expect(card.cardAssets!.length, equals(1));
        expect(card.cardAssets!.first.type, equals('image'));
      });

      test('应该正确处理后端返回的null字段', () {
        final backendData = {
          'id': 1,
          'type': null,
          'type_version': null,
          'title': null,
          'question': null,
          'answer': null,
          'extra': null,
          'schedule_id': null,
          'created_at': null,
          'updated_at': null,
          'user': null,
          'card_assets': null,
        };

        final card = CardModel.fromJson(backendData);

        expect(card.id, equals(1));
        expect(card.type, isNull);
        expect(card.typeVersion, equals(1)); // 默认值
        expect(card.title, isNull);
        expect(card.question, isNull);
        expect(card.answer, isNull);
        expect(card.extra, isNull);
        expect(card.scheduleId, isNull);
        expect(card.createdAt, isNull);
        expect(card.updatedAt, isNull);
        expect(card.user, isNull);
        expect(card.cardAssets, isNull);
      });
    });

    group('UserModel 数据同步', () {
      test('应该正确处理后端返回的用户数据', () {
        final backendData = {
          'id': 'user123',
          'username': 'testuser',
          'mobile': '13800138000',
          'email': '<EMAIL>',
          'avatar': 'https://example.com/avatar.jpg',
          'intro': '测试用户',
          'created_at': '2023-12-01T00:00:00Z',
          'updated_at': '2023-12-01T12:00:00Z',
          'config': {
            'id': 1,
            'is_auto_play_audio': true,
            'is_auto_play_ai_audio': false,
            'review_number': 10,
            'study_number': 5,
            'study_type': 1,
            'current_study_id': 1,
            'editing_book_id': 2,
            'created_at': '2023-12-01T00:00:00Z',
            'updated_at': '2023-12-01T12:00:00Z',
          },
          'token': 'jwt_token_here',
        };

        final user = UserModel.fromJson(backendData);

        expect(user.id, equals('user123'));
        expect(user.username, equals('testuser'));
        expect(user.mobile, equals('13800138000'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.avatar, equals('https://example.com/avatar.jpg'));
        expect(user.intro, equals('测试用户'));
        expect(user.createdAt, isNotNull);
        expect(user.updatedAt, isNotNull);
        expect(user.config, isNotNull);
        expect(user.config!.isAutoPlayAudio, isTrue);
        expect(user.config!.reviewNumber, equals(10));
        expect(user.token, equals('jwt_token_here'));
      });
    });

    group('错误数据处理', () {
      test('应该处理空的JSON对象', () {
        final emptyData = <String, Object?>{};

        expect(() => BookModel.fromJson(emptyData), returnsNormally);
        expect(() => CardModel.fromJson(emptyData), returnsNormally);
        expect(() => UserModel.fromJson(emptyData), returnsNormally);
      });

      test('应该处理包含意外字段的数据', () {
        final dataWithExtraFields = {
          'id': 1,
          'name': '测试书籍',
          'unexpected_field': 'unexpected_value',
          'another_field': {'nested': 'object'},
          'array_field': [1, 2, 3],
        };

        final book = BookModel.fromJson(dataWithExtraFields);
        expect(book.id, equals(1));
        expect(book.name, equals('测试书籍'));
      });
    });
  });
}
