import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/pages/card_editor/index.dart';

import '../card_editor_local_data_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<BookDataService>(),
  MockSpec<UserDataService>(),
])
void main() {
  group('卡片数据流集成测试', () {
    late MockCardDataService mockCardDataService;
    late MockBookDataService mockBookDataService;
    late MockUserDataService mockUserDataService;
    late CardEditorController cardEditorController;

    setUp(() {
      Get.testMode = true;
      Get.reset();
      
      // 设置模拟服务
      mockCardDataService = MockCardDataService();
      mockBookDataService = MockBookDataService();
      mockUserDataService = MockUserDataService();
      
      // 注册服务
      Get.put<CardDataService>(mockCardDataService);
      Get.put<BookDataService>(mockBookDataService);
      Get.put<UserDataService>(mockUserDataService);
      
      // 设置用户数据
      when(mockUserDataService.currentUser).thenReturn(
        UserModel(id: 1, username: 'testuser'),
      );
      
      cardEditorController = CardEditorController();
    });

    tearDown(() {
      Get.reset();
    });

    test('完整的卡片创建和同步流程', () async {
      // 1. 准备测试数据
      final testBook = BookModel(
        id: 1,
        name: '测试书籍',
        brief: '测试书籍描述',
      );
      
      final expectedCard = CardModel(
        id: 123,
        title: '测试卡片标题',
        question: '这是一个测试问题？',
        answer: '这是测试答案',
        type: 'language_general',
        typeVersion: 1,
      );

      // 2. 设置模拟返回值
      when(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => expectedCard);

      when(mockCardDataService.manualSync()).thenAnswer((_) async => true);

      // 3. 设置卡片编辑器数据
      cardEditorController.editType = EditType.create;
      cardEditorController.cardType = CardType.languageGeneral;
      cardEditorController.cardModelCreate = CardModelCreate(
        bookId: 1,
        title: '测试卡片标题',
        question: '这是一个测试问题？',
        answer: '这是测试答案',
        type: 'language_general',
        typeVersion: 1,
      );

      // 4. 执行创建操作
      await cardEditorController.createOrUpdateCard();

      // 5. 验证创建调用
      verify(mockCardDataService.createCard(
        bookId: 1,
        title: '测试卡片标题',
        question: '这是一个测试问题？',
        answer: '这是测试答案',
        type: 'language_general',
        typeVersion: 1,
        extra: null,
        assets: [],
      )).called(1);

      // 6. 执行手动同步
      final syncResult = await mockCardDataService.manualSync();
      expect(syncResult, isTrue);

      // 7. 验证同步调用
      verify(mockCardDataService.manualSync()).called(1);
    });

    test('卡片更新和同步流程', () async {
      // 1. 准备现有卡片数据
      final existingCard = CardModel(
        id: 456,
        title: '原始标题',
        question: '原始问题',
        answer: '原始答案',
        type: 'general',
      );

      // 2. 设置模拟返回值
      when(mockCardDataService.updateCard(
        any,
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => true);

      // 3. 设置编辑器为更新模式
      cardEditorController.editType = EditType.edit;
      cardEditorController.cardModel = existingCard;
      cardEditorController.cardModelCreate = CardModelCreate(
        id: 456,
        title: '更新后的标题',
        question: '更新后的问题',
        answer: '更新后的答案',
      );

      // 4. 执行更新操作
      await cardEditorController.createOrUpdateCard();

      // 5. 验证更新调用
      verify(mockCardDataService.updateCard(
        456,
        title: '更新后的标题',
        question: '更新后的问题',
        answer: '更新后的答案',
        extra: null,
        assets: [],
      )).called(1);
    });

    test('不同卡片类型的创建流程', () async {
      final testCases = [
        {
          'type': CardType.general,
          'expectedType': 'general',
          'title': '基础卡片',
          'question': '基础问题',
          'answer': '基础答案',
        },
        {
          'type': CardType.hanziWriter,
          'expectedType': 'hanzi_writer',
          'title': '汉字',
          'question': '书写提示',
          'answer': '',
        },
        {
          'type': CardType.languageListening,
          'expectedType': 'language_listening',
          'title': '听力练习',
          'question': '听音频选择答案',
          'answer': '正确答案',
        },
        {
          'type': CardType.readAloud,
          'expectedType': 'read_aloud',
          'title': '朗读练习',
          'question': '请朗读以下内容',
          'answer': '朗读要求',
        },
      ];

      for (final testCase in testCases) {
        // 重置模拟
        reset(mockCardDataService);
        
        final expectedCard = CardModel(
          id: 100,
          title: testCase['title'] as String,
          question: testCase['question'] as String,
          answer: testCase['answer'] as String,
          type: testCase['expectedType'] as String,
        );

        when(mockCardDataService.createCard(
          bookId: anyNamed('bookId'),
          title: anyNamed('title'),
          question: anyNamed('question'),
          answer: anyNamed('answer'),
          type: anyNamed('type'),
          typeVersion: anyNamed('typeVersion'),
          extra: anyNamed('extra'),
          assets: anyNamed('assets'),
        )).thenAnswer((_) async => expectedCard);

        // 设置卡片数据
        cardEditorController.editType = EditType.create;
        cardEditorController.cardType = testCase['type'] as CardType;
        cardEditorController.cardModelCreate = CardModelCreate(
          bookId: 1,
          title: testCase['title'] as String,
          question: testCase['question'] as String,
          answer: testCase['answer'] as String,
          type: testCase['expectedType'] as String,
        );

        // 执行创建
        await cardEditorController.createOrUpdateCard();

        // 验证调用
        verify(mockCardDataService.createCard(
          bookId: 1,
          title: testCase['title'] as String,
          question: testCase['question'] as String,
          answer: testCase['answer'] as String,
          type: testCase['expectedType'] as String,
          typeVersion: anyNamed('typeVersion'),
          extra: anyNamed('extra'),
          assets: anyNamed('assets'),
        )).called(1);
      }
    });

    test('预览数据实时更新', () {
      // 1. 设置初始数据
      cardEditorController.baseInfoController.text = '初始标题';
      cardEditorController.frontTextController.text = '初始问题';
      cardEditorController.backTextController.text = '初始答案';
      cardEditorController.cardType = CardType.general;

      // 2. 更新预览数据
      cardEditorController.updatePreviewData();

      // 3. 验证预览数据已更新
      expect(cardEditorController.cardModelCreate.title, equals('初始标题'));
      expect(cardEditorController.cardModelCreate.question, equals('初始问题'));
      expect(cardEditorController.cardModelCreate.answer, equals('初始答案'));
      expect(cardEditorController.cardModelCreate.type, equals('general'));

      // 4. 修改输入内容
      cardEditorController.baseInfoController.text = '更新标题';
      cardEditorController.frontTextController.text = '更新问题';
      cardEditorController.backTextController.text = '更新答案';
      cardEditorController.cardType = CardType.languageGeneral;

      // 5. 再次更新预览
      cardEditorController.updatePreviewData();

      // 6. 验证预览数据已同步更新
      expect(cardEditorController.cardModelCreate.title, equals('更新标题'));
      expect(cardEditorController.cardModelCreate.question, equals('更新问题'));
      expect(cardEditorController.cardModelCreate.answer, equals('更新答案'));
      expect(cardEditorController.cardModelCreate.type, equals('language_general'));
    });

    test('实时预览切换功能', () {
      // 1. 初始状态应该是关闭的
      expect(cardEditorController.showLivePreview, isFalse);

      // 2. 切换预览
      cardEditorController.toggleLivePreview();
      expect(cardEditorController.showLivePreview, isTrue);

      // 3. 再次切换
      cardEditorController.toggleLivePreview();
      expect(cardEditorController.showLivePreview, isFalse);
    });

    test('错误处理 - 创建失败', () async {
      // 1. 设置创建失败的情况
      when(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => null);

      // 2. 设置卡片数据
      cardEditorController.editType = EditType.create;
      cardEditorController.cardModelCreate = CardModelCreate(
        bookId: 1,
        title: '测试卡片',
        question: '测试问题',
        answer: '测试答案',
      );

      // 3. 执行创建（应该处理失败情况）
      await cardEditorController.createOrUpdateCard();

      // 4. 验证调用了创建方法
      verify(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).called(1);

      // 注意：实际的错误处理通过ShowToast显示，这里难以测试
      // 但我们确保了方法被正确调用
    });
  });
}
