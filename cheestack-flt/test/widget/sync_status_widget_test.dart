import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 生成Mock类
@GenerateMocks([ApiSyncService])
import '../sync_service_test.mocks.dart';

void main() {
  group('SyncStatusWidget 测试', () {
    late MockApiSyncService mockApiSyncService;

    setUp(() {
      mockApiSyncService = MockApiSyncService();
      Get.put<ApiSyncService>(mockApiSyncService);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('应该显示同步状态文本', (WidgetTester tester) async {
      // 构建widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SyncStatusWidget(),
          ),
        ),
      );

      // 验证显示正确的文本
      expect(find.text('数据同步正常'), findsOneWidget);
    });

    testWidgets('应该有正确的样式', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SyncStatusWidget(),
          ),
        ),
      );

      // 查找Container
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      // 获取Container widget
      final Container container = tester.widget(containerFinder);
      
      // 验证装饰
      expect(container.decoration, isA<BoxDecoration>());
      final BoxDecoration decoration = container.decoration as BoxDecoration;
      
      // 验证颜色和边框
      expect(decoration.color, equals(Colors.blue.withValues(alpha: 0.1)));
      expect(decoration.border, isA<Border>());
    });

    testWidgets('应该正确处理手动同步参数', (WidgetTester tester) async {
      bool syncCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SyncStatusWidget(
              showManualSync: true,
              onManualSync: () {
                syncCalled = true;
              },
            ),
          ),
        ),
      );

      // 验证组件正常显示
      expect(find.text('数据同步正常'), findsOneWidget);
      
      // 注意：当前简化版本没有手动同步按钮，所以这个测试主要验证参数不会导致错误
      expect(syncCalled, isFalse);
    });

    testWidgets('应该在不同主题下正常显示', (WidgetTester tester) async {
      // 测试深色主题
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.dark(),
          home: const Scaffold(
            body: SyncStatusWidget(),
          ),
        ),
      );

      expect(find.text('数据同步正常'), findsOneWidget);

      // 测试浅色主题
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: SyncStatusWidget(),
          ),
        ),
      );

      expect(find.text('数据同步正常'), findsOneWidget);
    });
  });

  group('SimpleSyncIndicator 测试', () {
    testWidgets('应该显示同步图标', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SimpleSyncIndicator(),
          ),
        ),
      );

      // 验证显示同步图标
      expect(find.byIcon(Icons.sync), findsOneWidget);
    });

    testWidgets('应该有正确的图标属性', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SimpleSyncIndicator(),
          ),
        ),
      );

      // 获取Icon widget
      final Icon icon = tester.widget(find.byIcon(Icons.sync));
      
      // 验证图标属性
      expect(icon.size, equals(16));
      expect(icon.color, equals(Colors.blue));
    });

    testWidgets('应该在不同尺寸下正常显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 100,
              height: 100,
              child: const SimpleSyncIndicator(),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.sync), findsOneWidget);

      // 测试更小的容器
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Container(
              width: 20,
              height: 20,
              child: const SimpleSyncIndicator(),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.sync), findsOneWidget);
    });
  });

  group('组件集成测试', () {
    testWidgets('应该能够在复杂布局中正常工作', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(
              title: const Text('测试页面'),
              actions: const [SimpleSyncIndicator()],
            ),
            body: const Column(
              children: [
                SyncStatusWidget(),
                Expanded(
                  child: Center(
                    child: Text('主要内容'),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // 验证所有组件都正常显示
      expect(find.text('测试页面'), findsOneWidget);
      expect(find.text('数据同步正常'), findsOneWidget);
      expect(find.text('主要内容'), findsOneWidget);
      expect(find.byIcon(Icons.sync), findsOneWidget);
    });

    testWidgets('应该能够处理多个实例', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                SyncStatusWidget(),
                SyncStatusWidget(),
                SimpleSyncIndicator(),
                SimpleSyncIndicator(),
              ],
            ),
          ),
        ),
      );

      // 验证多个实例都正常显示
      expect(find.text('数据同步正常'), findsNWidgets(2));
      expect(find.byIcon(Icons.sync), findsNWidgets(2));
    });
  });
}
