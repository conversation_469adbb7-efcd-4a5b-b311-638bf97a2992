// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in cheestack_flt/test/widget/sync_status_widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:cheestack_flt/services/index.dart' as _i3;
import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInternalFinalCallback_0<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeApiSyncService_1 extends _i1.SmartFake
    implements _i3.ApiSyncService {
  _FakeApiSyncService_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ApiSyncService].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiSyncService extends _i1.Mock implements _i3.ApiSyncService {
  MockApiSyncService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isSyncing => (super.noSuchMethod(
        Invocation.getter(#isSyncing),
        returnValue: false,
      ) as bool);

  @override
  String get syncStatus => (super.noSuchMethod(
        Invocation.getter(#syncStatus),
        returnValue: _i4.dummyValue<String>(
          this,
          Invocation.getter(#syncStatus),
        ),
      ) as String);

  @override
  double get syncProgress => (super.noSuchMethod(
        Invocation.getter(#syncProgress),
        returnValue: 0.0,
      ) as double);

  @override
  _i2.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<_i3.ApiSyncService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i5.Future<_i3.ApiSyncService>.value(_FakeApiSyncService_1(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i5.Future<_i3.ApiSyncService>);

  @override
  _i5.Future<bool> syncAllData({String? userId}) => (super.noSuchMethod(
        Invocation.method(
          #syncAllData,
          [],
          {#userId: userId},
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> syncStudyingCardsOnly({String? userId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncStudyingCardsOnly,
          [],
          {#userId: userId},
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> syncStudyingBooksCards({String? userId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncStudyingBooksCards,
          [],
          {#userId: userId},
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> syncTable(
    String? tableName, {
    String? userId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #syncTable,
          [tableName],
          {#userId: userId},
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
