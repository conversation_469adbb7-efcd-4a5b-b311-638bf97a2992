import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';

void main() {
  group('API端点验证测试', () {
    late Dio dio;

    setUpAll(() {
      dio = Dio(BaseOptions(
        baseUrl: 'http://*************:9000/api',
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 10),
      ));
    });

    group('API路径验证', () {
      test('书籍API端点应该可访问', () async {
        try {
          final response = await dio.get('/v1/books?skip=0&limit=1');

          // 验证响应格式
          expect(response.statusCode, equals(200));
          expect(response.data, isA<Map<String, dynamic>>());
          expect(response.data['code'], isNotNull);
          expect(response.data['msg'], isNotNull);
          expect(response.data['data'], isNotNull);

          print('✅ 书籍API响应: ${response.data}');
        } catch (e) {
          if (e is DioException &&
              (e.response?.statusCode == 200 ||
                  e.response?.statusCode == 401)) {
            // 即使认证失败，也说明端点是可访问的
            expect(e.response!.data['code'], isNotNull);
            print('✅ 书籍API端点可访问 (状态码: ${e.response?.statusCode})');
          } else {
            fail('书籍API端点不可访问: $e');
          }
        }
      });

      test('卡片API端点应该可访问', () async {
        try {
          final response = await dio.get('/v1/cards?skip=0&limit=1');

          expect(response.statusCode, equals(200));
          expect(response.data, isA<Map<String, dynamic>>());
          print('✅ 卡片API响应: ${response.data}');
        } catch (e) {
          if (e is DioException &&
              (e.response?.statusCode == 200 ||
                  e.response?.statusCode == 401)) {
            expect(e.response!.data['code'], isNotNull);
            print('✅ 卡片API端点可访问 (状态码: ${e.response?.statusCode})');
          } else {
            fail('卡片API端点不可访问: $e');
          }
        }
      });

      test('资源API端点应该可访问', () async {
        try {
          final response = await dio.get('/v1/assets?skip=0&limit=1');

          expect(response.statusCode, equals(200));
          expect(response.data, isA<Map<String, dynamic>>());
          print('✅ 资源API响应: ${response.data}');
        } catch (e) {
          if (e is DioException &&
              (e.response?.statusCode == 200 ||
                  e.response?.statusCode == 401)) {
            expect(e.response!.data['code'], isNotNull);
            print('✅ 资源API端点可访问 (状态码: ${e.response?.statusCode})');
          } else {
            fail('资源API端点不可访问: $e');
          }
        }
      });

      test('学习记录API端点应该可访问', () async {
        try {
          final response = await dio.get('/v1/study/my_reviews');

          expect(response.statusCode, equals(200));
          expect(response.data, isA<Map<String, dynamic>>());
          print('✅ 学习记录API响应: ${response.data}');
        } catch (e) {
          if (e is DioException &&
              (e.response?.statusCode == 200 ||
                  e.response?.statusCode == 401)) {
            expect(e.response!.data['code'], isNotNull);
            print('✅ 学习记录API端点可访问 (状态码: ${e.response?.statusCode})');
          } else {
            fail('学习记录API端点不可访问: $e');
          }
        }
      });

      test('书籍计划API端点应该可访问', () async {
        try {
          final response = await dio.get('/v1/book_schedules');

          expect(response.statusCode, equals(200));
          expect(response.data, isA<Map<String, dynamic>>());
          print('✅ 书籍计划API响应: ${response.data}');
        } catch (e) {
          if (e is DioException &&
              (e.response?.statusCode == 200 ||
                  e.response?.statusCode == 401)) {
            expect(e.response!.data['code'], isNotNull);
            print('✅ 书籍计划API端点可访问 (状态码: ${e.response?.statusCode})');
          } else {
            fail('书籍计划API端点不可访问: $e');
          }
        }
      });
    });

    group('URL构建验证', () {
      test('完整URL应该正确构建', () {
        const baseUrl = 'http://*************:9000/api';
        const endpoints = [
          '/v1/books',
          '/v1/cards',
          '/v1/assets',
          '/v1/study/my_reviews',
          '/v1/book_schedules',
          '/v1/study/records',
        ];

        for (final endpoint in endpoints) {
          final fullUrl = '$baseUrl$endpoint';

          // 验证URL格式
          expect(fullUrl, startsWith('http://'));
          expect(fullUrl, contains('*************:9000'));
          expect(fullUrl, contains('/api/v1/'));
          expect(fullUrl, isNot(contains('/api/api/')));
          expect(fullUrl, isNot(contains('/study/v1/')));

          print('✅ URL构建正确: $fullUrl');
        }
      });

      test('API路径不应包含重复的前缀', () {
        const problematicPaths = [
          '/api/api/v1/books',
          '/api/study/v1/books',
          '/study/v1/books',
        ];

        const correctPaths = [
          '/api/v1/books',
          '/api/v1/cards',
          '/api/v1/assets',
        ];

        // 验证不应该使用的路径
        for (final path in problematicPaths) {
          expect(
              path,
              anyOf([
                contains('/api/api/'),
                contains('/study/v1/'),
                allOf(startsWith('/study/'), isNot(startsWith('/api/'))),
              ]));
          print('❌ 错误路径格式: $path');
        }

        // 验证正确的路径
        for (final path in correctPaths) {
          expect(path, startsWith('/api/v1/'));
          expect(path, isNot(contains('/api/api/')));
          expect(path, isNot(contains('/study/v1/')));
          print('✅ 正确路径格式: $path');
        }
      });
    });

    group('响应格式验证', () {
      test('API响应应该有统一的格式', () async {
        try {
          final response = await dio.get('/v1/books?skip=0&limit=1');

          // 验证响应结构
          expect(response.data, isA<Map<String, dynamic>>());
          expect(response.data.containsKey('code'), isTrue);
          expect(response.data.containsKey('msg'), isTrue);
          expect(response.data.containsKey('data'), isTrue);

          // 验证字段类型
          expect(response.data['code'], isA<int>());
          expect(response.data['msg'], isA<String>());
          // data字段可以是List或其他类型

          print('✅ API响应格式正确: ${response.data.keys}');
        } catch (e) {
          if (e is DioException &&
              (e.response?.statusCode == 200 ||
                  e.response?.statusCode == 401)) {
            final data = e.response!.data;
            expect(data, isA<Map<String, dynamic>>());
            expect(data.containsKey('code'), isTrue);
            expect(data.containsKey('msg'), isTrue);
            expect(data.containsKey('data'), isTrue);
            print('✅ API响应格式正确 (状态码: ${e.response?.statusCode}): ${data.keys}');
          } else {
            fail('无法验证API响应格式: $e');
          }
        }
      });
    });
  });
}
