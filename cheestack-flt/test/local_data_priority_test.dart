import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

void main() {
  group('本地数据优先策略测试', () {
    setUp(() {
      Get.testMode = true;
      Get.reset();
    });

    tearDown(() {
      Get.reset();
    });

    test('应该能够检测BookDataService是否已注册', () {
      // Arrange - 未注册服务
      
      // Act
      final isRegistered = Get.isRegistered<String>(); // 使用String作为测试类型
      
      // Assert
      expect(isRegistered, isFalse);
      
      // Arrange - 注册服务
      Get.put<String>('test_service');
      
      // Act
      final isRegisteredAfter = Get.isRegistered<String>();
      
      // Assert
      expect(isRegisteredAfter, isTrue);
    });

    test('应该能够正确处理空的搜索关键词', () {
      // Arrange
      String searchKeyword = '';
      
      // Act
      bool isEmpty = searchKeyword.isEmpty;
      
      // Assert
      expect(isEmpty, isTrue);
      
      // Arrange
      searchKeyword = '测试';
      
      // Act
      isEmpty = searchKeyword.isEmpty;
      
      // Assert
      expect(isEmpty, isFalse);
    });

    test('应该能够正确处理书籍列表筛选', () {
      // Arrange
      final books = [
        {'id': 1, 'name': '公开书籍', 'privacy': 'public'},
        {'id': 2, 'name': '私有书籍', 'privacy': 'private'},
        {'id': 3, 'name': '另一个公开书籍', 'privacy': 'public'},
      ];
      
      // Act - 筛选公开书籍
      final publicBooks = books.where((book) => book['privacy'] == 'public').toList();
      
      // Assert
      expect(publicBooks.length, equals(2));
      expect(publicBooks.every((book) => book['privacy'] == 'public'), isTrue);
      
      // Act - 筛选私有书籍
      final privateBooks = books.where((book) => book['privacy'] == 'private').toList();
      
      // Assert
      expect(privateBooks.length, equals(1));
      expect(privateBooks.first['privacy'], equals('private'));
    });

    test('应该能够正确处理分页参数', () {
      // Arrange
      final bookList = List.generate(10, (index) => {'id': index, 'name': '书籍$index'});
      final limit = 20;
      
      // Act
      final offset = bookList.length;
      
      // Assert
      expect(offset, equals(10));
      expect(limit, equals(20));
      
      // 模拟分页逻辑
      final hasMore = offset < 100; // 假设总共有100本书
      expect(hasMore, isTrue);
    });

    test('应该能够正确处理卡片搜索逻辑', () {
      // Arrange
      final cards = [
        {'id': 1, 'title': '测试卡片1', 'question': '这是一个测试问题'},
        {'id': 2, 'title': '普通卡片', 'question': '这是普通问题'},
        {'id': 3, 'title': '测试卡片2', 'question': '另一个测试问题'},
      ];
      final keyword = '测试';
      
      // Act - 模拟搜索逻辑
      final searchResults = cards.where((card) {
        final title = card['title'] as String;
        final question = card['question'] as String;
        return title.contains(keyword) || question.contains(keyword);
      }).toList();
      
      // Assert
      expect(searchResults.length, equals(2));
      expect(searchResults.every((card) {
        final title = card['title'] as String;
        final question = card['question'] as String;
        return title.contains(keyword) || question.contains(keyword);
      }), isTrue);
    });

    test('应该能够正确处理错误回退逻辑', () {
      // Arrange
      bool useLocalService = true;
      bool localServiceAvailable = false;
      
      // Act
      bool shouldUseLocal = useLocalService && localServiceAvailable;
      
      // Assert
      expect(shouldUseLocal, isFalse);
      
      // Arrange - 本地服务可用
      localServiceAvailable = true;
      
      // Act
      shouldUseLocal = useLocalService && localServiceAvailable;
      
      // Assert
      expect(shouldUseLocal, isTrue);
    });

    test('应该能够正确处理书籍删除逻辑', () {
      // Arrange
      final bookList = [
        {'id': 1, 'name': '书籍1'},
        {'id': 2, 'name': '书籍2'},
        {'id': 3, 'name': '书籍3'},
      ];
      final bookIdToDelete = 2;
      
      // Act
      final initialCount = bookList.length;
      bookList.removeWhere((book) => book['id'] == bookIdToDelete);
      final finalCount = bookList.length;
      
      // Assert
      expect(initialCount, equals(3));
      expect(finalCount, equals(2));
      expect(bookList.any((book) => book['id'] == bookIdToDelete), isFalse);
      expect(bookList.map((book) => book['id']).toList(), equals([1, 3]));
    });

    test('应该能够正确处理书籍复制逻辑', () {
      // Arrange
      final originalBook = {
        'id': 1,
        'name': '原始书籍',
        'brief': '原始简介',
        'privacy': 'private',
      };
      
      // Act - 模拟复制逻辑
      final duplicatedBook = Map<String, dynamic>.from(originalBook);
      duplicatedBook['id'] = 2; // 新ID
      duplicatedBook['name'] = '${originalBook['name']} - 副本';
      
      // Assert
      expect(duplicatedBook['id'], equals(2));
      expect(duplicatedBook['name'], equals('原始书籍 - 副本'));
      expect(duplicatedBook['brief'], equals(originalBook['brief']));
      expect(duplicatedBook['privacy'], equals(originalBook['privacy']));
      
      // 确保原始书籍未被修改
      expect(originalBook['name'], equals('原始书籍'));
      expect(originalBook['id'], equals(1));
    });

    test('应该能够正确处理排序逻辑', () {
      // Arrange
      final books = [
        {'id': 1, 'name': 'B书籍', 'created_at': '2023-01-01'},
        {'id': 2, 'name': 'A书籍', 'created_at': '2023-01-03'},
        {'id': 3, 'name': 'C书籍', 'created_at': '2023-01-02'},
      ];
      
      // Act - 按创建时间降序排序
      books.sort((a, b) {
        final dateA = DateTime.parse(a['created_at'] as String);
        final dateB = DateTime.parse(b['created_at'] as String);
        return dateB.compareTo(dateA); // 降序
      });
      
      // Assert
      expect(books[0]['id'], equals(2)); // 最新的
      expect(books[1]['id'], equals(3)); // 中间的
      expect(books[2]['id'], equals(1)); // 最旧的
      
      // Act - 按名称升序排序
      books.sort((a, b) {
        final nameA = a['name'] as String;
        final nameB = b['name'] as String;
        return nameA.compareTo(nameB); // 升序
      });
      
      // Assert
      expect(books[0]['name'], equals('A书籍'));
      expect(books[1]['name'], equals('B书籍'));
      expect(books[2]['name'], equals('C书籍'));
    });
  });
}
