import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/models/index.dart';

void main() {
  group('数据模型测试', () {
    test('BookModel 应该正确处理null值', () {
      // 测试null值处理
      final book = BookModel(
        id: 1,
        name: null,
        brief: null,
        cover: null,
        privacy: null,
        createdAt: null,
        updatedAt: null,
      );

      expect(book.id, equals(1));
      expect(book.name, isNull);
      expect(book.brief, isNull);
      expect(book.cover, isNull);
      expect(book.privacy, isNull);
      expect(book.createdAt, isNull);
      expect(book.updatedAt, isNull);
    });

    test('BookModel 应该正确处理默认值', () {
      final book = BookModel(
        id: 1,
        name: '',
        brief: '',
        cover: '',
        privacy: 'free',
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      );

      expect(book.id, equals(1));
      expect(book.name, equals(''));
      expect(book.brief, equals(''));
      expect(book.cover, equals(''));
      expect(book.privacy, equals('free'));
      expect(book.createdAt, isNotNull);
      expect(book.updatedAt, isNotNull);
    });

    test('BookModel.fromJson 应该正确处理API数据', () {
      final jsonData = {
        'id': 1,
        'name': '测试书籍',
        'brief': '这是一个测试书籍',
        'cover': 'https://example.com/cover.jpg',
        'privacy': 'free',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
        'user': null,
      };

      final book = BookModel.fromJson(jsonData);

      expect(book.id, equals(1));
      expect(book.name, equals('测试书籍'));
      expect(book.brief, equals('这是一个测试书籍'));
      expect(book.cover, equals('https://example.com/cover.jpg'));
      expect(book.privacy, equals('free'));
      expect(book.createdAt, equals('2024-01-01T00:00:00Z'));
      expect(book.updatedAt, equals('2024-01-01T00:00:00Z'));
      expect(book.user, isNull);
    });

    test('BookModel.fromJson 应该正确处理null字段', () {
      final jsonData = {
        'id': 1,
        'name': null,
        'brief': null,
        'cover': null,
        'privacy': null,
        'created_at': null,
        'updated_at': null,
        'user': null,
      };

      final book = BookModel.fromJson(jsonData);

      expect(book.id, equals(1));
      expect(book.name, isNull);
      expect(book.brief, isNull);
      expect(book.cover, isNull);
      expect(book.privacy, isNull);
      expect(book.createdAt, isNull);
      expect(book.updatedAt, isNull);
      expect(book.user, isNull);
    });

    test('CardModel 应该正确处理null值', () {
      final card = CardModel(
        id: 1,
        type: null,
        typeVersion: null,
        title: null,
        question: null,
        answer: null,
        extra: null,
        scheduleId: null,
        createdAt: null,
        updatedAt: null,
      );

      expect(card.id, equals(1));
      expect(card.type, isNull);
      expect(card.typeVersion, isNull);
      expect(card.title, isNull);
      expect(card.question, isNull);
      expect(card.answer, isNull);
      expect(card.extra, isNull);
      expect(card.scheduleId, isNull);
      expect(card.createdAt, isNull);
      expect(card.updatedAt, isNull);
    });

    test('CardModel 应该正确处理默认值', () {
      final card = CardModel(
        id: 1,
        type: 'general',
        typeVersion: 1,
        title: '测试卡片',
        question: '这是问题',
        answer: '这是答案',
        extra: null,
        scheduleId: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(card.id, equals(1));
      expect(card.type, equals('general'));
      expect(card.typeVersion, equals(1));
      expect(card.title, equals('测试卡片'));
      expect(card.question, equals('这是问题'));
      expect(card.answer, equals('这是答案'));
      expect(card.extra, isNull);
      expect(card.scheduleId, isNull);
      expect(card.createdAt, isNotNull);
      expect(card.updatedAt, isNotNull);
    });
  });

  group('字符串处理测试', () {
    test('应该正确处理空字符串', () {
      final userId = '';
      expect(userId.isEmpty, isTrue);
    });

    test('应该正确处理null字符串', () {
      String? userId;
      expect(userId, isNull);
      expect(userId?.isEmpty, isNull);
    });

    test('应该正确处理有效字符串', () {
      const userId = 'user123';
      expect(userId.isEmpty, isFalse);
      expect(userId.isNotEmpty, isTrue);
    });
  });

  group('日期时间处理测试', () {
    test('应该正确处理ISO8601日期字符串', () {
      final dateString = DateTime.now().toIso8601String();
      expect(dateString, isNotNull);
      expect(dateString.isNotEmpty, isTrue);
      
      // 验证可以解析回DateTime
      final parsedDate = DateTime.parse(dateString);
      expect(parsedDate, isA<DateTime>());
    });

    test('应该正确处理null日期', () {
      String? dateString;
      expect(dateString, isNull);
      
      // 使用默认值
      final defaultDate = dateString ?? DateTime.now().toIso8601String();
      expect(defaultDate, isNotNull);
      expect(defaultDate.isNotEmpty, isTrue);
    });
  });
}
