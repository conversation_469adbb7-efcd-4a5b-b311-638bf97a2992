// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in cheestack_flt/test/services/card_data_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i13;

import 'package:cheestack_flt/models/index.dart' as _i11;
import 'package:cheestack_flt/services/dao/book_dao.dart' as _i3;
import 'package:cheestack_flt/services/dao/card_asset_dao.dart' as _i5;
import 'package:cheestack_flt/services/dao/card_dao.dart' as _i4;
import 'package:cheestack_flt/services/dao/dao_manager.dart' as _i9;
import 'package:cheestack_flt/services/dao/study_record_dao.dart' as _i6;
import 'package:cheestack_flt/services/dao/sync_record_dao.dart' as _i7;
import 'package:cheestack_flt/services/dao/user_dao.dart' as _i2;
import 'package:cheestack_flt/services/index.dart' as _i12;
import 'package:get/get.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i14;
import 'package:sqflite/sqflite.dart' as _i10;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserDao_0 extends _i1.SmartFake implements _i2.UserDao {
  _FakeUserDao_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserConfigDao_1 extends _i1.SmartFake implements _i2.UserConfigDao {
  _FakeUserConfigDao_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBookDao_2 extends _i1.SmartFake implements _i3.BookDao {
  _FakeBookDao_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCardDao_3 extends _i1.SmartFake implements _i4.CardDao {
  _FakeCardDao_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCardAssetDao_4 extends _i1.SmartFake implements _i5.CardAssetDao {
  _FakeCardAssetDao_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStudyRecordDao_5 extends _i1.SmartFake
    implements _i6.StudyRecordDao {
  _FakeStudyRecordDao_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSyncRecordDao_6 extends _i1.SmartFake implements _i7.SyncRecordDao {
  _FakeSyncRecordDao_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInternalFinalCallback_7<T> extends _i1.SmartFake
    implements _i8.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDaoManager_8 extends _i1.SmartFake implements _i9.DaoManager {
  _FakeDaoManager_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabase_9 extends _i1.SmartFake implements _i10.Database {
  _FakeDatabase_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCardModel_10 extends _i1.SmartFake implements _i11.CardModel {
  _FakeCardModel_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCardAsset_11 extends _i1.SmartFake implements _i11.CardAsset {
  _FakeCardAsset_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStudyRecord_12 extends _i1.SmartFake implements _i6.StudyRecord {
  _FakeStudyRecord_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserDataService_13 extends _i1.SmartFake
    implements _i12.UserDataService {
  _FakeUserDataService_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [DaoManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockDaoManager extends _i1.Mock implements _i9.DaoManager {
  @override
  _i2.UserDao get userDao => (super.noSuchMethod(
        Invocation.getter(#userDao),
        returnValue: _FakeUserDao_0(
          this,
          Invocation.getter(#userDao),
        ),
        returnValueForMissingStub: _FakeUserDao_0(
          this,
          Invocation.getter(#userDao),
        ),
      ) as _i2.UserDao);

  @override
  _i2.UserConfigDao get userConfigDao => (super.noSuchMethod(
        Invocation.getter(#userConfigDao),
        returnValue: _FakeUserConfigDao_1(
          this,
          Invocation.getter(#userConfigDao),
        ),
        returnValueForMissingStub: _FakeUserConfigDao_1(
          this,
          Invocation.getter(#userConfigDao),
        ),
      ) as _i2.UserConfigDao);

  @override
  _i3.BookDao get bookDao => (super.noSuchMethod(
        Invocation.getter(#bookDao),
        returnValue: _FakeBookDao_2(
          this,
          Invocation.getter(#bookDao),
        ),
        returnValueForMissingStub: _FakeBookDao_2(
          this,
          Invocation.getter(#bookDao),
        ),
      ) as _i3.BookDao);

  @override
  _i4.CardDao get cardDao => (super.noSuchMethod(
        Invocation.getter(#cardDao),
        returnValue: _FakeCardDao_3(
          this,
          Invocation.getter(#cardDao),
        ),
        returnValueForMissingStub: _FakeCardDao_3(
          this,
          Invocation.getter(#cardDao),
        ),
      ) as _i4.CardDao);

  @override
  _i5.CardAssetDao get cardAssetDao => (super.noSuchMethod(
        Invocation.getter(#cardAssetDao),
        returnValue: _FakeCardAssetDao_4(
          this,
          Invocation.getter(#cardAssetDao),
        ),
        returnValueForMissingStub: _FakeCardAssetDao_4(
          this,
          Invocation.getter(#cardAssetDao),
        ),
      ) as _i5.CardAssetDao);

  @override
  _i6.StudyRecordDao get studyRecordDao => (super.noSuchMethod(
        Invocation.getter(#studyRecordDao),
        returnValue: _FakeStudyRecordDao_5(
          this,
          Invocation.getter(#studyRecordDao),
        ),
        returnValueForMissingStub: _FakeStudyRecordDao_5(
          this,
          Invocation.getter(#studyRecordDao),
        ),
      ) as _i6.StudyRecordDao);

  @override
  _i7.SyncRecordDao get syncRecordDao => (super.noSuchMethod(
        Invocation.getter(#syncRecordDao),
        returnValue: _FakeSyncRecordDao_6(
          this,
          Invocation.getter(#syncRecordDao),
        ),
        returnValueForMissingStub: _FakeSyncRecordDao_6(
          this,
          Invocation.getter(#syncRecordDao),
        ),
      ) as _i7.SyncRecordDao);

  @override
  _i8.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i8.InternalFinalCallback<void>);

  @override
  _i8.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i8.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i13.Future<_i9.DaoManager> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i13.Future<_i9.DaoManager>.value(_FakeDaoManager_8(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i13.Future<_i9.DaoManager>.value(_FakeDaoManager_8(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i13.Future<_i9.DaoManager>);

  @override
  List<dynamic> getAllDaos() => (super.noSuchMethod(
        Invocation.method(
          #getAllDaos,
          [],
        ),
        returnValue: <dynamic>[],
        returnValueForMissingStub: <dynamic>[],
      ) as List<dynamic>);

  @override
  _i13.Future<void> clearAllData() => (super.noSuchMethod(
        Invocation.method(
          #clearAllData,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<Map<String, int>> getDatabaseStats() => (super.noSuchMethod(
        Invocation.method(
          #getDatabaseStats,
          [],
        ),
        returnValue: _i13.Future<Map<String, int>>.value(<String, int>{}),
        returnValueForMissingStub:
            _i13.Future<Map<String, int>>.value(<String, int>{}),
      ) as _i13.Future<Map<String, int>>);

  @override
  _i13.Future<Map<String, dynamic>> getUserDataStats(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserDataStats,
          [userId],
        ),
        returnValue:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i13.Future<Map<String, dynamic>>);

  @override
  _i13.Future<Map<String, List<String>>> checkDataIntegrity() =>
      (super.noSuchMethod(
        Invocation.method(
          #checkDataIntegrity,
          [],
        ),
        returnValue: _i13.Future<Map<String, List<String>>>.value(
            <String, List<String>>{}),
        returnValueForMissingStub: _i13.Future<Map<String, List<String>>>.value(
            <String, List<String>>{}),
      ) as _i13.Future<Map<String, List<String>>>);

  @override
  _i13.Future<void> fixDataIntegrity() => (super.noSuchMethod(
        Invocation.method(
          #fixDataIntegrity,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> optimizeDatabase() => (super.noSuchMethod(
        Invocation.method(
          #optimizeDatabase,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<Map<String, List<Map<String, dynamic>>>> backupDatabase() =>
      (super.noSuchMethod(
        Invocation.method(
          #backupDatabase,
          [],
        ),
        returnValue: _i13.Future<Map<String, List<Map<String, dynamic>>>>.value(
            <String, List<Map<String, dynamic>>>{}),
        returnValueForMissingStub:
            _i13.Future<Map<String, List<Map<String, dynamic>>>>.value(
                <String, List<Map<String, dynamic>>>{}),
      ) as _i13.Future<Map<String, List<Map<String, dynamic>>>>);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [CardDao].
///
/// See the documentation for Mockito's code generation for more information.
class MockCardDao extends _i1.Mock implements _i4.CardDao {
  @override
  String get tableName => (super.noSuchMethod(
        Invocation.getter(#tableName),
        returnValue: _i14.dummyValue<String>(
          this,
          Invocation.getter(#tableName),
        ),
        returnValueForMissingStub: _i14.dummyValue<String>(
          this,
          Invocation.getter(#tableName),
        ),
      ) as String);

  @override
  String get primaryKey => (super.noSuchMethod(
        Invocation.getter(#primaryKey),
        returnValue: _i14.dummyValue<String>(
          this,
          Invocation.getter(#primaryKey),
        ),
        returnValueForMissingStub: _i14.dummyValue<String>(
          this,
          Invocation.getter(#primaryKey),
        ),
      ) as String);

  @override
  bool get useAutoIncrement => (super.noSuchMethod(
        Invocation.getter(#useAutoIncrement),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i10.Database get db => (super.noSuchMethod(
        Invocation.getter(#db),
        returnValue: _FakeDatabase_9(
          this,
          Invocation.getter(#db),
        ),
        returnValueForMissingStub: _FakeDatabase_9(
          this,
          Invocation.getter(#db),
        ),
      ) as _i10.Database);

  @override
  _i11.CardModel fromMap(Map<String, dynamic>? map) => (super.noSuchMethod(
        Invocation.method(
          #fromMap,
          [map],
        ),
        returnValue: _FakeCardModel_10(
          this,
          Invocation.method(
            #fromMap,
            [map],
          ),
        ),
        returnValueForMissingStub: _FakeCardModel_10(
          this,
          Invocation.method(
            #fromMap,
            [map],
          ),
        ),
      ) as _i11.CardModel);

  @override
  Map<String, dynamic> toMap(_i11.CardModel? entity) => (super.noSuchMethod(
        Invocation.method(
          #toMap,
          [entity],
        ),
        returnValue: <String, dynamic>{},
        returnValueForMissingStub: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i13.Future<int> insertWithIds(
    _i11.CardModel? card,
    String? userId,
    int? bookId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertWithIds,
          [
            card,
            userId,
            bookId,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<_i11.CardModel>> findByBookId(
    int? bookId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByBookId,
          [bookId],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<List<_i11.CardModel>> findByUserId(
    String? userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByUserId,
          [userId],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<List<_i11.CardModel>> findByType(
    String? userId,
    String? type, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByType,
          [
            userId,
            type,
          ],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<List<_i11.CardModel>> search(
    String? userId,
    String? keyword, {
    int? bookId,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #search,
          [
            userId,
            keyword,
          ],
          {
            #bookId: bookId,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<int> updateContent(
    int? cardId, {
    String? title,
    String? question,
    String? answer,
    dynamic extra,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateContent,
          [cardId],
          {
            #title: title,
            #question: question,
            #answer: answer,
            #extra: extra,
          },
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> updateScheduleId(
    int? cardId,
    int? scheduleId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateScheduleId,
          [
            cardId,
            scheduleId,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> countByBookId(int? bookId) => (super.noSuchMethod(
        Invocation.method(
          #countByBookId,
          [bookId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> countByUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #countByUserId,
          [userId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> countByUserIdAndType(
    String? userId,
    String? type,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #countByUserIdAndType,
          [
            userId,
            type,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> deleteAllByBookId(int? bookId) => (super.noSuchMethod(
        Invocation.method(
          #deleteAllByBookId,
          [bookId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> deleteAllByUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #deleteAllByUserId,
          [userId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> batchMoveToBook(
    List<int>? cardIds,
    int? targetBookId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #batchMoveToBook,
          [
            cardIds,
            targetBookId,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> batchDelete(List<int>? cardIds) => (super.noSuchMethod(
        Invocation.method(
          #batchDelete,
          [cardIds],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<_i11.CardModel>> findCardsForReview(
    String? userId, {
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findCardsForReview,
          [userId],
          {#limit: limit},
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<List<_i11.CardModel>> findNewCards(
    String? userId, {
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findNewCards,
          [userId],
          {#limit: limit},
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<void> addSyncRecord(
    String? action,
    String? recordId,
    Map<String, dynamic>? dataBefore,
    Map<String, dynamic>? dataAfter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addSyncRecord,
          [
            action,
            recordId,
            dataBefore,
            dataAfter,
          ],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<int> insert(_i11.CardModel? entity) => (super.noSuchMethod(
        Invocation.method(
          #insert,
          [entity],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<int>> insertAll(List<_i11.CardModel>? entities) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertAll,
          [entities],
        ),
        returnValue: _i13.Future<List<int>>.value(<int>[]),
        returnValueForMissingStub: _i13.Future<List<int>>.value(<int>[]),
      ) as _i13.Future<List<int>>);

  @override
  _i13.Future<int> update(_i11.CardModel? entity) => (super.noSuchMethod(
        Invocation.method(
          #update,
          [entity],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> delete(dynamic id) => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [id],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<_i11.CardModel?> findById(dynamic id) => (super.noSuchMethod(
        Invocation.method(
          #findById,
          [id],
        ),
        returnValue: _i13.Future<_i11.CardModel?>.value(),
        returnValueForMissingStub: _i13.Future<_i11.CardModel?>.value(),
      ) as _i13.Future<_i11.CardModel?>);

  @override
  _i13.Future<List<_i11.CardModel>> findAll({
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findAll,
          [],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<List<_i11.CardModel>> findWhere({
    String? where,
    List<Object?>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findWhere,
          [],
          {
            #where: where,
            #whereArgs: whereArgs,
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);

  @override
  _i13.Future<int> count({
    String? where,
    List<Object?>? whereArgs,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #count,
          [],
          {
            #where: where,
            #whereArgs: whereArgs,
          },
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<void> markAsSynced(
    dynamic id,
    String? syncedAt,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #markAsSynced,
          [
            id,
            syncedAt,
          ],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<List<_i11.CardModel>> findDirty() => (super.noSuchMethod(
        Invocation.method(
          #findDirty,
          [],
        ),
        returnValue:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardModel>>.value(<_i11.CardModel>[]),
      ) as _i13.Future<List<_i11.CardModel>>);
}

/// A class which mocks [CardAssetDao].
///
/// See the documentation for Mockito's code generation for more information.
class MockCardAssetDao extends _i1.Mock implements _i5.CardAssetDao {
  @override
  String get tableName => (super.noSuchMethod(
        Invocation.getter(#tableName),
        returnValue: _i14.dummyValue<String>(
          this,
          Invocation.getter(#tableName),
        ),
        returnValueForMissingStub: _i14.dummyValue<String>(
          this,
          Invocation.getter(#tableName),
        ),
      ) as String);

  @override
  String get primaryKey => (super.noSuchMethod(
        Invocation.getter(#primaryKey),
        returnValue: _i14.dummyValue<String>(
          this,
          Invocation.getter(#primaryKey),
        ),
        returnValueForMissingStub: _i14.dummyValue<String>(
          this,
          Invocation.getter(#primaryKey),
        ),
      ) as String);

  @override
  bool get useAutoIncrement => (super.noSuchMethod(
        Invocation.getter(#useAutoIncrement),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i10.Database get db => (super.noSuchMethod(
        Invocation.getter(#db),
        returnValue: _FakeDatabase_9(
          this,
          Invocation.getter(#db),
        ),
        returnValueForMissingStub: _FakeDatabase_9(
          this,
          Invocation.getter(#db),
        ),
      ) as _i10.Database);

  @override
  _i11.CardAsset fromMap(Map<String, dynamic>? map) => (super.noSuchMethod(
        Invocation.method(
          #fromMap,
          [map],
        ),
        returnValue: _FakeCardAsset_11(
          this,
          Invocation.method(
            #fromMap,
            [map],
          ),
        ),
        returnValueForMissingStub: _FakeCardAsset_11(
          this,
          Invocation.method(
            #fromMap,
            [map],
          ),
        ),
      ) as _i11.CardAsset);

  @override
  Map<String, dynamic> toMap(_i11.CardAsset? entity) => (super.noSuchMethod(
        Invocation.method(
          #toMap,
          [entity],
        ),
        returnValue: <String, dynamic>{},
        returnValueForMissingStub: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i13.Future<List<_i11.CardAsset>> findByCardId(int? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByCardId,
          [cardId],
        ),
        returnValue:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
      ) as _i13.Future<List<_i11.CardAsset>>);

  @override
  _i13.Future<List<_i11.CardAsset>> findByType(
    int? cardId,
    String? type,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByType,
          [
            cardId,
            type,
          ],
        ),
        returnValue:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
      ) as _i13.Future<List<_i11.CardAsset>>);

  @override
  _i13.Future<int> insertCardAsset(_i11.CardAsset? asset) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertCardAsset,
          [asset],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<int>> insertCardAssets(
    List<_i11.CardAsset>? assets,
    int? cardId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertCardAssets,
          [
            assets,
            cardId,
          ],
        ),
        returnValue: _i13.Future<List<int>>.value(<int>[]),
        returnValueForMissingStub: _i13.Future<List<int>>.value(<int>[]),
      ) as _i13.Future<List<int>>);

  @override
  _i13.Future<int> updateCardAsset(_i11.CardAsset? asset) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCardAsset,
          [asset],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> deleteByCardId(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #deleteByCardId,
          [cardId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> deleteByCardIdAndType(
    int? cardId,
    String? type,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteByCardIdAndType,
          [
            cardId,
            type,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> countByCardId(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #countByCardId,
          [cardId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> countByCardIdAndType(
    int? cardId,
    String? type,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #countByCardIdAndType,
          [
            cardId,
            type,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> batchDelete(List<int>? assetIds) => (super.noSuchMethod(
        Invocation.method(
          #batchDelete,
          [assetIds],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> updateUrl(
    int? assetId,
    String? url,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUrl,
          [
            assetId,
            url,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> updateName(
    int? assetId,
    String? name,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateName,
          [
            assetId,
            name,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> updateText(
    int? assetId,
    String? text,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateText,
          [
            assetId,
            text,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> updateIsCorrect(
    int? assetId,
    bool? isCorrect,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateIsCorrect,
          [
            assetId,
            isCorrect,
          ],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<void> addSyncRecord(
    String? action,
    String? recordId,
    Map<String, dynamic>? dataBefore,
    Map<String, dynamic>? dataAfter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addSyncRecord,
          [
            action,
            recordId,
            dataBefore,
            dataAfter,
          ],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<int> insert(_i11.CardAsset? entity) => (super.noSuchMethod(
        Invocation.method(
          #insert,
          [entity],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<int>> insertAll(List<_i11.CardAsset>? entities) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertAll,
          [entities],
        ),
        returnValue: _i13.Future<List<int>>.value(<int>[]),
        returnValueForMissingStub: _i13.Future<List<int>>.value(<int>[]),
      ) as _i13.Future<List<int>>);

  @override
  _i13.Future<int> update(_i11.CardAsset? entity) => (super.noSuchMethod(
        Invocation.method(
          #update,
          [entity],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> delete(dynamic id) => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [id],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<_i11.CardAsset?> findById(dynamic id) => (super.noSuchMethod(
        Invocation.method(
          #findById,
          [id],
        ),
        returnValue: _i13.Future<_i11.CardAsset?>.value(),
        returnValueForMissingStub: _i13.Future<_i11.CardAsset?>.value(),
      ) as _i13.Future<_i11.CardAsset?>);

  @override
  _i13.Future<List<_i11.CardAsset>> findAll({
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findAll,
          [],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
      ) as _i13.Future<List<_i11.CardAsset>>);

  @override
  _i13.Future<List<_i11.CardAsset>> findWhere({
    String? where,
    List<Object?>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findWhere,
          [],
          {
            #where: where,
            #whereArgs: whereArgs,
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
      ) as _i13.Future<List<_i11.CardAsset>>);

  @override
  _i13.Future<int> count({
    String? where,
    List<Object?>? whereArgs,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #count,
          [],
          {
            #where: where,
            #whereArgs: whereArgs,
          },
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<void> markAsSynced(
    dynamic id,
    String? syncedAt,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #markAsSynced,
          [
            id,
            syncedAt,
          ],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<List<_i11.CardAsset>> findDirty() => (super.noSuchMethod(
        Invocation.method(
          #findDirty,
          [],
        ),
        returnValue:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i11.CardAsset>>.value(<_i11.CardAsset>[]),
      ) as _i13.Future<List<_i11.CardAsset>>);
}

/// A class which mocks [StudyRecordDao].
///
/// See the documentation for Mockito's code generation for more information.
class MockStudyRecordDao extends _i1.Mock implements _i6.StudyRecordDao {
  @override
  String get tableName => (super.noSuchMethod(
        Invocation.getter(#tableName),
        returnValue: _i14.dummyValue<String>(
          this,
          Invocation.getter(#tableName),
        ),
        returnValueForMissingStub: _i14.dummyValue<String>(
          this,
          Invocation.getter(#tableName),
        ),
      ) as String);

  @override
  String get primaryKey => (super.noSuchMethod(
        Invocation.getter(#primaryKey),
        returnValue: _i14.dummyValue<String>(
          this,
          Invocation.getter(#primaryKey),
        ),
        returnValueForMissingStub: _i14.dummyValue<String>(
          this,
          Invocation.getter(#primaryKey),
        ),
      ) as String);

  @override
  bool get useAutoIncrement => (super.noSuchMethod(
        Invocation.getter(#useAutoIncrement),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i10.Database get db => (super.noSuchMethod(
        Invocation.getter(#db),
        returnValue: _FakeDatabase_9(
          this,
          Invocation.getter(#db),
        ),
        returnValueForMissingStub: _FakeDatabase_9(
          this,
          Invocation.getter(#db),
        ),
      ) as _i10.Database);

  @override
  _i6.StudyRecord fromMap(Map<String, dynamic>? map) => (super.noSuchMethod(
        Invocation.method(
          #fromMap,
          [map],
        ),
        returnValue: _FakeStudyRecord_12(
          this,
          Invocation.method(
            #fromMap,
            [map],
          ),
        ),
        returnValueForMissingStub: _FakeStudyRecord_12(
          this,
          Invocation.method(
            #fromMap,
            [map],
          ),
        ),
      ) as _i6.StudyRecord);

  @override
  Map<String, dynamic> toMap(_i6.StudyRecord? entity) => (super.noSuchMethod(
        Invocation.method(
          #toMap,
          [entity],
        ),
        returnValue: <String, dynamic>{},
        returnValueForMissingStub: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i13.Future<int> insertStudyRecord(_i6.StudyRecord? record) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertStudyRecord,
          [record],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findByUserId(
    String? userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByUserId,
          [userId],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findByCardId(
    int? cardId, {
    String? orderBy,
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByCardId,
          [cardId],
          {
            #orderBy: orderBy,
            #limit: limit,
          },
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);

  @override
  _i13.Future<_i6.StudyRecord?> findLatestByCardId(int? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #findLatestByCardId,
          [cardId],
        ),
        returnValue: _i13.Future<_i6.StudyRecord?>.value(),
        returnValueForMissingStub: _i13.Future<_i6.StudyRecord?>.value(),
      ) as _i13.Future<_i6.StudyRecord?>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findByDateRange(
    String? userId,
    DateTime? startDate,
    DateTime? endDate, {
    String? orderBy,
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByDateRange,
          [
            userId,
            startDate,
            endDate,
          ],
          {
            #orderBy: orderBy,
            #limit: limit,
          },
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findByRating(
    String? userId,
    int? rating, {
    String? orderBy,
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByRating,
          [
            userId,
            rating,
          ],
          {
            #orderBy: orderBy,
            #limit: limit,
          },
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findByState(
    String? userId,
    int? state, {
    String? orderBy,
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findByState,
          [
            userId,
            state,
          ],
          {
            #orderBy: orderBy,
            #limit: limit,
          },
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);

  @override
  _i13.Future<Map<String, dynamic>> getUserStudyStats(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserStudyStats,
          [userId],
        ),
        returnValue:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i13.Future<Map<String, dynamic>>);

  @override
  _i13.Future<Map<String, dynamic>> getTodayStudyStats(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTodayStudyStats,
          [userId],
        ),
        returnValue:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i13.Future<Map<String, dynamic>>);

  @override
  _i13.Future<int> getStudyStreak(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #getStudyStreak,
          [userId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<int>> getCardsForReview(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardsForReview,
          [userId],
        ),
        returnValue: _i13.Future<List<int>>.value(<int>[]),
        returnValueForMissingStub: _i13.Future<List<int>>.value(<int>[]),
      ) as _i13.Future<List<int>>);

  @override
  _i13.Future<int> deleteByCardId(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #deleteByCardId,
          [cardId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> deleteByUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #deleteByUserId,
          [userId],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<void> addSyncRecord(
    String? action,
    String? recordId,
    Map<String, dynamic>? dataBefore,
    Map<String, dynamic>? dataAfter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #addSyncRecord,
          [
            action,
            recordId,
            dataBefore,
            dataAfter,
          ],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<int> insert(_i6.StudyRecord? entity) => (super.noSuchMethod(
        Invocation.method(
          #insert,
          [entity],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<List<int>> insertAll(List<_i6.StudyRecord>? entities) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertAll,
          [entities],
        ),
        returnValue: _i13.Future<List<int>>.value(<int>[]),
        returnValueForMissingStub: _i13.Future<List<int>>.value(<int>[]),
      ) as _i13.Future<List<int>>);

  @override
  _i13.Future<int> update(_i6.StudyRecord? entity) => (super.noSuchMethod(
        Invocation.method(
          #update,
          [entity],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<int> delete(dynamic id) => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [id],
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<_i6.StudyRecord?> findById(dynamic id) => (super.noSuchMethod(
        Invocation.method(
          #findById,
          [id],
        ),
        returnValue: _i13.Future<_i6.StudyRecord?>.value(),
        returnValueForMissingStub: _i13.Future<_i6.StudyRecord?>.value(),
      ) as _i13.Future<_i6.StudyRecord?>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findAll({
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findAll,
          [],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findWhere({
    String? where,
    List<Object?>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findWhere,
          [],
          {
            #where: where,
            #whereArgs: whereArgs,
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);

  @override
  _i13.Future<int> count({
    String? where,
    List<Object?>? whereArgs,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #count,
          [],
          {
            #where: where,
            #whereArgs: whereArgs,
          },
        ),
        returnValue: _i13.Future<int>.value(0),
        returnValueForMissingStub: _i13.Future<int>.value(0),
      ) as _i13.Future<int>);

  @override
  _i13.Future<void> markAsSynced(
    dynamic id,
    String? syncedAt,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #markAsSynced,
          [
            id,
            syncedAt,
          ],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<List<_i6.StudyRecord>> findDirty() => (super.noSuchMethod(
        Invocation.method(
          #findDirty,
          [],
        ),
        returnValue:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
        returnValueForMissingStub:
            _i13.Future<List<_i6.StudyRecord>>.value(<_i6.StudyRecord>[]),
      ) as _i13.Future<List<_i6.StudyRecord>>);
}

/// A class which mocks [UserDataService].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserDataService extends _i1.Mock implements _i12.UserDataService {
  @override
  _i8.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i8.InternalFinalCallback<void>);

  @override
  _i8.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_7<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i8.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i13.Future<_i12.UserDataService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue:
            _i13.Future<_i12.UserDataService>.value(_FakeUserDataService_13(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i13.Future<_i12.UserDataService>.value(_FakeUserDataService_13(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i13.Future<_i12.UserDataService>);

  @override
  _i13.Future<void> reloadCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #reloadCurrentUser,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<bool> syncCurrentUserFromApi() => (super.noSuchMethod(
        Invocation.method(
          #syncCurrentUserFromApi,
          [],
        ),
        returnValue: _i13.Future<bool>.value(false),
        returnValueForMissingStub: _i13.Future<bool>.value(false),
      ) as _i13.Future<bool>);

  @override
  _i13.Future<bool> loginUser(
    String? mobile,
    String? verificationCode,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginUser,
          [
            mobile,
            verificationCode,
          ],
        ),
        returnValue: _i13.Future<bool>.value(false),
        returnValueForMissingStub: _i13.Future<bool>.value(false),
      ) as _i13.Future<bool>);

  @override
  _i13.Future<void> logoutUser() => (super.noSuchMethod(
        Invocation.method(
          #logoutUser,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<bool> updateUserProfile({
    String? username,
    String? intro,
    String? avatar,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserProfile,
          [],
          {
            #username: username,
            #intro: intro,
            #avatar: avatar,
          },
        ),
        returnValue: _i13.Future<bool>.value(false),
        returnValueForMissingStub: _i13.Future<bool>.value(false),
      ) as _i13.Future<bool>);

  @override
  _i13.Future<bool> updateUserConfig({
    bool? isAutoPlayAudio,
    bool? isAutoPlayAiAudio,
    int? reviewNumber,
    int? studyNumber,
    int? studyType,
    int? currentStudyId,
    int? editingBookId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserConfig,
          [],
          {
            #isAutoPlayAudio: isAutoPlayAudio,
            #isAutoPlayAiAudio: isAutoPlayAiAudio,
            #reviewNumber: reviewNumber,
            #studyNumber: studyNumber,
            #studyType: studyType,
            #currentStudyId: currentStudyId,
            #editingBookId: editingBookId,
          },
        ),
        returnValue: _i13.Future<bool>.value(false),
        returnValueForMissingStub: _i13.Future<bool>.value(false),
      ) as _i13.Future<bool>);

  @override
  _i13.Future<Map<String, dynamic>> getUserStats() => (super.noSuchMethod(
        Invocation.method(
          #getUserStats,
          [],
        ),
        returnValue:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i13.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i13.Future<Map<String, dynamic>>);

  @override
  bool isLoggedIn() => (super.noSuchMethod(
        Invocation.method(
          #isLoggedIn,
          [],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
