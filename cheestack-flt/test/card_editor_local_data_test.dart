import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cheestack_flt/pages/card_editor/index.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/common/enums/index.dart';

import 'card_editor_local_data_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<CardDataService>(),
])
void main() {
  group('卡片编辑器本地数据操作测试', () {
    late CardEditorController controller;
    late MockCardDataService mockCardDataService;

    setUp(() {
      Get.testMode = true;
      Get.reset();
      
      mockCardDataService = MockCardDataService();
      Get.put<CardDataService>(mockCardDataService);
      
      controller = CardEditorController();
    });

    tearDown(() {
      Get.reset();
    });

    test('应该能够创建新卡片使用本地数据服务', () async {
      // Arrange
      controller.editType = EditType.create;
      controller.cardModelCreate = CardModelCreate(
        bookId: 1,
        title: '测试卡片',
        question: '测试问题',
        answer: '测试答案',
        type: 'basic',
        typeVersion: 1,
      );
      
      final expectedCard = CardModel(
        id: 123,
        title: '测试卡片',
        question: '测试问题',
        answer: '测试答案',
        type: 'basic',
        typeVersion: 1,
      );

      when(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => expectedCard);

      // Act
      await controller.createOrUpdateCard();

      // Assert
      verify(mockCardDataService.createCard(
        bookId: 1,
        title: '测试卡片',
        question: '测试问题',
        answer: '测试答案',
        type: 'basic',
        typeVersion: 1,
        extra: null,
        assets: [],
      )).called(1);
    });

    test('应该能够更新现有卡片使用本地数据服务', () async {
      // Arrange
      controller.editType = EditType.edit;
      controller.cardModel = CardModel(id: 1, title: '原始卡片');
      controller.cardModelCreate = CardModelCreate(
        id: 1,
        title: '更新的卡片',
        question: '更新的问题',
        answer: '更新的答案',
      );

      when(mockCardDataService.updateCard(
        any,
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => true);

      // Act
      await controller.createOrUpdateCard();

      // Assert
      verify(mockCardDataService.updateCard(
        1,
        title: '更新的卡片',
        question: '更新的问题',
        answer: '更新的答案',
        extra: null,
        assets: [],
      )).called(1);
    });

    test('创建卡片失败时应该显示错误消息', () async {
      // Arrange
      controller.editType = EditType.create;
      controller.cardModelCreate = CardModelCreate(
        bookId: 1,
        title: '测试卡片',
        question: '测试问题',
        answer: '测试答案',
      );

      when(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => null);

      // Act
      await controller.createOrUpdateCard();

      // Assert
      verify(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).called(1);
      
      // 这里应该显示错误消息，但由于ShowToast是静态方法，难以测试
      // 在实际应用中，错误会通过ShowToast.fail显示
    });

    test('更新卡片失败时应该显示错误消息', () async {
      // Arrange
      controller.editType = EditType.edit;
      controller.cardModel = CardModel(id: 1, title: '原始卡片');
      controller.cardModelCreate = CardModelCreate(
        id: 1,
        title: '更新的卡片',
      );

      when(mockCardDataService.updateCard(
        any,
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      )).thenAnswer((_) async => false);

      // Act
      await controller.createOrUpdateCard();

      // Assert
      verify(mockCardDataService.updateCard(
        1,
        title: '更新的卡片',
        question: null,
        answer: null,
        extra: null,
        assets: [],
      )).called(1);
    });

    test('当CardDataService未注册时应该回退到网络API', () async {
      // Arrange
      Get.delete<CardDataService>();
      controller.editType = EditType.create;
      controller.cardModelCreate = CardModelCreate(
        bookId: 1,
        title: '测试卡片',
      );

      // Act
      await controller.createOrUpdateCard();

      // Assert
      // 由于没有注册CardDataService，应该回退到网络API
      // 这里我们验证没有调用本地服务
      verifyNever(mockCardDataService.createCard(
        bookId: anyNamed('bookId'),
        title: anyNamed('title'),
        question: anyNamed('question'),
        answer: anyNamed('answer'),
        type: anyNamed('type'),
        typeVersion: anyNamed('typeVersion'),
        extra: anyNamed('extra'),
        assets: anyNamed('assets'),
      ));
    });

    test('updatePreviewData应该更新预览数据', () {
      // Arrange
      controller.baseInfoController.text = '新标题';
      controller.frontTextController.text = '新问题';
      controller.backTextController.text = '新答案';
      controller.cardType = CardType.languageGeneral;

      // Act
      controller.updatePreviewData();

      // Assert
      expect(controller.cardModelCreate.title, equals('新标题'));
      expect(controller.cardModelCreate.question, equals('新问题'));
      expect(controller.cardModelCreate.answer, equals('新答案'));
      expect(controller.cardModelCreate.type, equals('language_general'));
    });

    test('应该正确处理不同的卡片类型', () {
      // Arrange & Act & Assert
      final testCases = [
        {'type': CardType.general, 'expected': 'general'},
        {'type': CardType.hanziWriter, 'expected': 'hanzi_writer'},
        {'type': CardType.languageGeneral, 'expected': 'language_general'},
        {'type': CardType.readAloud, 'expected': 'read_aloud'},
      ];

      for (final testCase in testCases) {
        controller.cardType = testCase['type'] as CardType;
        controller.updatePreviewData();
        expect(controller.cardModelCreate.type, equals(testCase['expected']));
      }
    });
  });
}
