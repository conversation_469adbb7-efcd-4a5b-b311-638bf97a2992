part of services;

/// 学习数据服务
/// 负责管理学习记录和统计数据的本地存储和同步
class StudyDataService extends GetxService {
  static StudyDataService get to => Get.find();

  late final DaoManager _daoManager;
  late final UserDataService _userDataService;

  Future<StudyDataService> init() async {
    // 等待依赖服务初始化完成
    while (!Get.isRegistered<DaoManager>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }
    while (!Get.isRegistered<UserDataService>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }

    _daoManager = DaoManager.to;
    _userDataService = UserDataService.to;
    return this;
  }

  /// 记录学习
  Future<bool> recordStudy({
    required int cardId,
    required int rating, // 1-5分评分
    DateTime? reviewTime,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return false;

    try {
      // 获取卡片的最新学习记录
      final lastRecord =
          await _daoManager.studyRecordDao.findLatestByCardId(cardId);

      // 计算FSRS参数
      final now = reviewTime ?? DateTime.now();
      final elapsedDays =
          lastRecord != null ? now.difference(lastRecord.reviewTime).inDays : 0;

      // 根据评分计算下次复习间隔（简化版FSRS算法）
      final scheduledDays =
          _calculateScheduledDays(rating, elapsedDays, lastRecord);

      final record = StudyRecord(
        userId: userId,
        cardId: cardId,
        rating: rating,
        elapsedDays: elapsedDays,
        scheduledDays: scheduledDays,
        reviewTime: now,
        state: _calculateCardState(rating, lastRecord),
        createdAt: now,
        updatedAt: now,
      );

      await _daoManager.studyRecordDao.insertStudyRecord(record);

      // 同步到服务器（后台进行）
      _syncStudyRecordToApi(record);

      Console.log('Study record created successfully');
      return true;
    } catch (e) {
      Console.log('Failed to record study: $e');
      return false;
    }
  }

  /// 获取用户学习统计
  Future<Map<String, dynamic>> getUserStudyStats() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return {};

    try {
      return await _daoManager.studyRecordDao.getUserStudyStats(userId);
    } catch (e) {
      Console.log('Failed to get user study stats: $e');
      return {};
    }
  }

  /// 获取今日学习统计
  Future<Map<String, dynamic>> getTodayStudyStats() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return {};

    try {
      return await _daoManager.studyRecordDao.getTodayStudyStats(userId);
    } catch (e) {
      Console.log('Failed to get today study stats: $e');
      return {};
    }
  }

  /// 获取学习连续天数
  Future<int> getStudyStreak() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return 0;

    try {
      return await _daoManager.studyRecordDao.getStudyStreak(userId);
    } catch (e) {
      Console.log('Failed to get study streak: $e');
      return 0;
    }
  }

  /// 获取卡片的学习记录
  Future<List<StudyRecord>> getCardStudyRecords(
    int cardId, {
    int? limit,
  }) async {
    try {
      return await _daoManager.studyRecordDao.findByCardId(
        cardId,
        limit: limit,
      );
    } catch (e) {
      Console.log('Failed to get card study records: $e');
      return [];
    }
  }

  /// 获取用户的学习记录
  Future<List<StudyRecord>> getUserStudyRecords({
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];

    try {
      return await _daoManager.studyRecordDao.findByUserId(
        userId,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      Console.log('Failed to get user study records: $e');
      return [];
    }
  }

  /// 获取指定日期范围的学习记录
  Future<List<StudyRecord>> getStudyRecordsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    int? limit,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];

    try {
      return await _daoManager.studyRecordDao.findByDateRange(
        userId,
        startDate,
        endDate,
        limit: limit,
      );
    } catch (e) {
      Console.log('Failed to get study records by date range: $e');
      return [];
    }
  }

  /// 获取需要复习的卡片ID列表
  Future<List<int>> getCardsForReview() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];

    try {
      return await _daoManager.studyRecordDao.getCardsForReview(userId);
    } catch (e) {
      Console.log('Failed to get cards for review: $e');
      return [];
    }
  }

  /// 获取所有在学习中的卡片ID列表（包括复习和学习中的）
  Future<List<int>> getStudyingCardIds() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];

    try {
      // 获取需要复习的卡片
      final reviewCards =
          await _daoManager.studyRecordDao.getCardsForReview(userId);

      // 获取正在学习中的卡片（有学习记录但还在学习阶段的）
      final result = await _daoManager.studyRecordDao.db.rawQuery('''
        SELECT DISTINCT sr1.card_id
        FROM study_records sr1
        INNER JOIN (
          SELECT card_id, MAX(review_time) as latest_review
          FROM study_records
          WHERE user_id = ?
          GROUP BY card_id
        ) sr2 ON sr1.card_id = sr2.card_id AND sr1.review_time = sr2.latest_review
        WHERE sr1.user_id = ?
          AND sr1.state IN (1, 2, 3)
      ''', [userId, userId]);

      final studyingCards = result.map((row) => row['card_id'] as int).toList();

      // 合并去重
      return <int>{...reviewCards, ...studyingCards}.toList();
    } catch (e) {
      Console.log('Failed to get studying card IDs: $e');
      return [];
    }
  }

  /// 获取在学习中的书籍ID列表
  Future<List<int>> getStudyingBookIds() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];

    try {
      // 查询有学习记录的书籍ID
      final result = await _daoManager.studyRecordDao.db.rawQuery('''
        SELECT DISTINCT ubc.book_id, MAX(sr.review_time) as latest_review
        FROM study_records sr
        INNER JOIN user_book_cards ubc ON sr.card_id = ubc.card_id
        WHERE sr.user_id = ?
          AND ubc.user_id = ?
        GROUP BY ubc.book_id
        ORDER BY latest_review DESC
      ''', [userId, userId]);

      return result.map((row) => row['book_id'] as int).toList();
    } catch (e) {
      Console.log('Failed to get studying book IDs: $e');
      return [];
    }
  }

  /// 获取学习日历数据
  Future<Map<String, int>> getStudyCalendarData({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return {};

    try {
      final start =
          startDate ?? DateTime.now().subtract(const Duration(days: 365));
      final end = endDate ?? DateTime.now();

      final records = await _daoManager.studyRecordDao.findByDateRange(
        userId,
        start,
        end,
      );

      final calendarData = <String, int>{};

      for (final record in records) {
        final dateKey = record.reviewTime.toIso8601String().split('T')[0];
        calendarData[dateKey] = (calendarData[dateKey] ?? 0) + 1;
      }

      return calendarData;
    } catch (e) {
      Console.log('Failed to get study calendar data: $e');
      return {};
    }
  }

  /// 获取学习趋势数据
  Future<List<Map<String, dynamic>>> getStudyTrendData({
    int days = 30,
  }) async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return [];

    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));

      final records = await _daoManager.studyRecordDao.findByDateRange(
        userId,
        startDate,
        endDate,
      );

      final trendData = <Map<String, dynamic>>[];

      for (int i = 0; i < days; i++) {
        final date = startDate.add(Duration(days: i));
        final dateKey = date.toIso8601String().split('T')[0];

        final dayRecords = records.where((record) {
          final recordDate = record.reviewTime.toIso8601String().split('T')[0];
          return recordDate == dateKey;
        }).toList();

        trendData.add({
          'date': dateKey,
          'count': dayRecords.length,
          'avg_rating': dayRecords.isNotEmpty
              ? dayRecords.map((r) => r.rating).reduce((a, b) => a + b) /
                  dayRecords.length
              : 0.0,
        });
      }

      return trendData;
    } catch (e) {
      Console.log('Failed to get study trend data: $e');
      return [];
    }
  }

  /// 从API同步学习记录
  Future<bool> syncStudyRecordsFromApi() async {
    final userId = _userDataService.currentUser?.id;
    if (userId == null) return false;

    try {
      Console.log('Syncing study records from API...');

      // 获取学习计划和记录
      final response = await OxHttp.to.get('/api/v1/study/my_reviews');
      if (response.statusCode == 200) {
        final List<dynamic> reviewsData = response.data['data'] ?? [];

        for (final _ in reviewsData) {
          // 这里需要根据后端的实际数据结构来处理
          // 由于后端的Schedule和Record模型比较复杂，
          // 我们可以在用户实际学习时再创建记录
        }
      }

      Console.log('Study records synced from API successfully');
      return true;
    } catch (e) {
      Console.log('Failed to sync study records from API: $e');
      return false;
    }
  }

  /// 简化版FSRS算法
  int _calculateScheduledDays(
      int rating, int elapsedDays, StudyRecord? lastRecord) {
    if (lastRecord == null) {
      // 新卡片
      switch (rating) {
        case 1: // Again
          return 0; // 立即重新学习
        case 2: // Hard
          return 1;
        case 3: // Good
          return 1;
        case 4: // Easy
          return 4;
        default:
          return 1;
      }
    }

    final lastInterval = lastRecord.scheduledDays;

    switch (rating) {
      case 1: // Again
        return (lastInterval * 0.2).round().clamp(1, 7);
      case 2: // Hard
        return (lastInterval * 0.6).round().clamp(1, lastInterval);
      case 3: // Good
        return (lastInterval * 2.5).round();
      case 4: // Easy
        return (lastInterval * 4.0).round();
      default:
        return lastInterval;
    }
  }

  /// 计算卡片状态
  int _calculateCardState(int rating, StudyRecord? lastRecord) {
    if (lastRecord == null) {
      return rating >= 3 ? 1 : 0; // 学习中或新卡片
    }

    if (rating < 3) {
      return 3; // 重新学习
    } else {
      return 2; // 复习中
    }
  }

  /// 同步学习记录到API
  Future<void> _syncStudyRecordToApi(StudyRecord record) async {
    try {
      final data = {
        'card_id': record.cardId,
        'rating': record.rating,
        'elapsed_days': record.elapsedDays,
        'scheduled_days': record.scheduledDays,
        'review_time': record.reviewTime.toIso8601String(),
        'state': record.state,
      };

      await OxHttp.to.post('/api/v1/study/records', data: data);
      Console.log('Study record synced to API');
    } catch (e) {
      Console.log('Failed to sync study record to API: $e');
    }
  }

  /// 批量同步学习记录到API
  Future<void> batchSyncStudyRecordsToApi(List<StudyRecord> records) async {
    try {
      final data = records
          .map((record) => {
                'card_id': record.cardId,
                'rating': record.rating,
                'elapsed_days': record.elapsedDays,
                'scheduled_days': record.scheduledDays,
                'review_time': record.reviewTime.toIso8601String(),
                'state': record.state,
              })
          .toList();

      await OxHttp.to
          .post('/api/v1/study/records/bulk', data: {'records': data});
      Console.log('Study records batch synced to API');
    } catch (e) {
      Console.log('Failed to batch sync study records to API: $e');
    }
  }
}
