part of extensions;

typedef OnFailure = Function(HttpException? httpException);
typedef OnSuccess = Function(dynamic data);

extension ControllerExt on GetxController {
  /// 异步请求
  Future<void> asyncRequest(
    Future<HttpResponse> Function() request, {
    required OnSuccess? onSuccess,
    OnFailure? onFailure,
  }) async {
    try {
      HttpResponse httpResponse = await request();

      /// 业务处理
      if (httpResponse.ok) {
        onSuccess?.call(httpResponse.data);
      } else {
        // 如果有自定义的失败处理，使用自定义处理
        if (onFailure != null) {
          onFailure.call(httpResponse.error);
        } else {
          // 否则显示用户友好的错误消息
          final friendlyMessage =
              ErrorHandler.getUserFriendlyMessage(httpResponse.error);
          ShowToast.fail(friendlyMessage);
        }
      }
    } on Exception catch (e) {
      // 统一处理异常
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);

      // 如果有自定义的失败处理，创建一个HttpException并传递
      if (onFailure != null) {
        final httpException = HttpException(friendlyMessage);
        onFailure.call(httpException);
      } else {
        // 否则直接显示用户友好的错误消息
        ShowToast.fail(friendlyMessage);
      }
    }
  }
}
