part of oxhttp;

/// 单例模式
class HttpResponse {
  bool ok = false;
  dynamic data;
  HttpException? error;
  int? statusCode;

  static final HttpResponse _instance = HttpResponse._internal();

  /// 工厂构造方法，这里使用命名构造函数方式进行声明
  factory HttpResponse.getInstance() => _instance;

  HttpResponse._internal();

  /// 命名构造函数
  HttpResponse.success(this.data, {this.statusCode}) {
    ok = true;
  }

  /// 命名构造函数
  HttpResponse.failure({String? errorMsg, int? errorCode}) {
    final friendlyMsg =
        ErrorHandler.getUserFriendlyMessage(errorMsg ?? 'request failed');
    error = BadRequestException(msg: friendlyMsg, code: errorCode);
    statusCode = errorCode;
    ok = false;
  }

  /// 命名构造函数
  HttpResponse.failureFormResponse({dynamic data}) {
    error = BadResponseException(data);
    ok = false;
  }

  /// 命名构造函数
  HttpResponse.failureFromError([HttpException? error]) {
    if (error != null) {
      final friendlyMsg = ErrorHandler.getUserFriendlyMessage(error.msg);
      this.error = HttpException(friendlyMsg, error.code);
    } else {
      this.error = UnknownException(
          ErrorHandler.getUserFriendlyMessage('unknown error'));
    }
    ok = false;
  }
}

HttpResponse handleResponse(Response? response,
    {HttpTransformer? httpTransformer}) {
  httpTransformer ??= HttpTransformerDefault.getInstance();

  // 返回值异常
  if (response == null) {
    return HttpResponse.failureFromError();
  }

  // token失效
  if (_isTokenTimeout(response.statusCode)) {
    /// 如果token已失效, 则跳转到登录界面

    return HttpResponse.failureFromError(
        UnauthorisedException(
        msg: ErrorHandler.getUserFriendlyMessage("没有权限"),
        code: response.statusCode));
  }
  // 接口调用成功
  if (_isRequestSuccess(response.statusCode)) {
    final result = httpTransformer.parse(response);
    result.statusCode = response.statusCode;
    return result;
  } else {
    // 接口调用失败
    return HttpResponse.failure(
        errorMsg: ErrorHandler.getUserFriendlyMessage(
            response.statusMessage ?? 'request failed'),
        errorCode: response.statusCode);
  }
}

HttpResponse handleException(Exception exception) {
  var parseException = _parseException(exception);
  return HttpResponse.failureFromError(parseException);
}

/// 鉴权失败
bool _isTokenTimeout(int? code) {
  return code == 401;
}

/// 请求成功
bool _isRequestSuccess(int? statusCode) {
  return (statusCode != null && statusCode >= 200 && statusCode < 300);
}

HttpException _parseException(Exception error) {
  if (error is DioException) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
        return NetworkException(
            msg: ErrorHandler.getUserFriendlyMessage(
                error.message ?? 'network error'));
      case DioExceptionType.cancel:
        return CancelException(
            ErrorHandler.getUserFriendlyMessage(error.message ?? 'cancelled'));
      case DioExceptionType.badResponse:
        try {
          int? errCode = error.response?.statusCode;
          String msg = error.response?.data["msg"];

          switch (errCode) {
            case 400:
              return BadRequestException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            case 401:
              return UnauthorisedException(
                  msg: ErrorHandler.getUserFriendlyMessage("请重新登录"),
                  code: errCode);
            case 403:
              return BadRequestException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            case 404:
              return BadRequestException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            case 405:
              return BadRequestException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            case 500:
              return BadServiceException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            case 502:
              return BadServiceException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            case 503:
              return BadServiceException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            case 505:
              return UnauthorisedException(
                  msg: ErrorHandler.getUserFriendlyMessage(msg), code: errCode);
            default:
              return UnknownException(ErrorHandler.getUserFriendlyMessage(msg));
          }
        } on Exception catch (_) {
          return UnknownException(ErrorHandler.getUserFriendlyMessage("未知错误"));
        }

      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return NetworkException(
              msg: ErrorHandler.getUserFriendlyMessage("服务器连接失败"));
          // return NetworkException(message: error.message);
        } else {
          return UnknownException(ErrorHandler.getUserFriendlyMessage(
              error.message ?? 'unknown error'));
        }
      default:
        return UnknownException(ErrorHandler.getUserFriendlyMessage(
            error.message ?? 'unknown error'));
    }
  } else {
    return UnknownException(
        ErrorHandler.getUserFriendlyMessage(error.toString()));
  }
}
