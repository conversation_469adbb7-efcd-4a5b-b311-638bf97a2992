import 'dart:io';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import '../services/network_service.dart';

/// 统一的错误处理工具类
///
/// 负责将技术性错误转换为用户友好的提示信息
class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  /// 获取用户友好的错误消息
  static String getUserFriendlyMessage(dynamic error) {
    if (error == null) {
      return '操作失败，请稍后重试';
    }

    // 处理字符串类型的错误
    if (error is String) {
      return _mapStringError(error);
    }

    // 处理DioException
    if (error is DioException) {
      return _mapDioError(error);
    }

    // 处理HttpException (OxHttp系统)
    if (error.runtimeType.toString().contains('HttpException')) {
      return _mapHttpException(error);
    }

    // 处理SocketException
    if (error is SocketException) {
      return '网络连接失败，请检查网络设置';
    }

    // 处理其他异常
    if (error is Exception) {
      return _mapGeneralException(error);
    }

    // 处理包含msg字段的对象
    if (error is Map && error.containsKey('msg')) {
      return _mapStringError(error['msg']?.toString() ?? '');
    }

    // 处理具有msg属性的对象
    try {
      final msg = error.msg;
      if (msg != null) {
        return _mapStringError(msg.toString());
      }
    } catch (e) {
      // 忽略反射错误
    }

    // 默认处理
    return _mapStringError(error.toString());
  }

  /// 映射字符串错误消息
  static String _mapStringError(String error) {
    final lowerError = error.toLowerCase();

    // 特殊处理包含"code: -1, msg:"格式的错误信息
    if (error.contains('code:') && error.contains('msg:')) {
      final msgMatch = RegExp(r'msg:\s*(.+)').firstMatch(error);
      if (msgMatch != null) {
        final msgPart = msgMatch.group(1)?.trim() ?? '';
        if (msgPart.isNotEmpty) {
          return _mapStringError(msgPart);
        }
      }
    }

    // 网络连接相关错误
    if (lowerError.contains('connection refused') ||
        lowerError.contains('connection failed') ||
        lowerError.contains('connection error') ||
        lowerError.contains('network error') ||
        lowerError.contains('no internet') ||
        lowerError.contains('network unreachable')) {
      return '网络连接失败，请检查网络设置后重试';
    }

    // 超时相关错误
    if (lowerError.contains('timeout') ||
        lowerError.contains('time out') ||
        lowerError.contains('超时')) {
      return '请求超时，请检查网络后重试';
    }

    // 服务器相关错误
    if (lowerError.contains('server error') ||
        lowerError.contains('internal server error') ||
        lowerError.contains('500') ||
        lowerError.contains('502') ||
        lowerError.contains('503') ||
        lowerError.contains('服务器错误')) {
      return '服务暂时不可用，请稍后重试';
    }

    // 认证相关错误
    if (lowerError.contains('unauthorized') ||
        lowerError.contains('401') ||
        lowerError.contains('token') ||
        lowerError.contains('认证失败') ||
        lowerError.contains('没有权限')) {
      return '登录已过期，请重新登录';
    }

    // 权限相关错误
    if (lowerError.contains('forbidden') ||
        lowerError.contains('403') ||
        lowerError.contains('权限不足')) {
      return '权限不足，无法执行此操作';
    }

    // 资源不存在错误
    if (lowerError.contains('not found') ||
        lowerError.contains('404') ||
        lowerError.contains('不存在')) {
      return '请求的内容不存在';
    }

    // 参数错误
    if (lowerError.contains('bad request') ||
        lowerError.contains('400') ||
        lowerError.contains('参数错误') ||
        lowerError.contains('validation error') ||
        lowerError.contains('数据校验错误')) {
      return '请求参数有误，请检查后重试';
    }

    // 取消请求
    if (lowerError.contains('cancel') ||
        lowerError.contains('cancelled') ||
        lowerError.contains('取消')) {
      return '操作已取消';
    }

    // 解析错误
    if (lowerError.contains('parse') ||
        lowerError.contains('format') ||
        lowerError.contains('解析')) {
      return '数据格式错误，请稍后重试';
    }

    // 如果包含中文，可能已经是用户友好的消息
    if (_containsChinese(error)) {
      return error;
    }

    // 默认返回通用错误消息
    return '操作失败，请稍后重试';
  }

  /// 映射Dio错误
  static String _mapDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return '请求超时，请检查网络后重试';

      case DioExceptionType.cancel:
        return '操作已取消';

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final responseData = error.response?.data;

        // 尝试从响应中获取错误消息
        String? serverMessage;
        if (responseData is Map<String, dynamic>) {
          serverMessage = responseData['msg'] ?? responseData['message'];
        }

        if (serverMessage != null && _containsChinese(serverMessage)) {
          return serverMessage;
        }

        // 根据状态码返回友好消息
        switch (statusCode) {
          case 400:
            return '请求参数有误，请检查后重试';
          case 401:
            return '登录已过期，请重新登录';
          case 403:
            return '权限不足，无法执行此操作';
          case 404:
            return '请求的内容不存在';
          case 500:
          case 502:
          case 503:
            return '服务暂时不可用，请稍后重试';
          default:
            return '网络请求失败，请稍后重试';
        }

      case DioExceptionType.unknown:
      default:
        if (error.error is SocketException) {
          return '网络连接失败，请检查网络设置';
        }
        return '网络连接异常，请检查网络后重试';
    }
  }

  /// 映射HttpException (OxHttp系统)
  static String _mapHttpException(dynamic error) {
    try {
      final msg = error.msg?.toString() ?? '';
      final code = error.code ?? -1;

      // 根据错误代码映射
      switch (code) {
        case 401:
          return '登录已过期，请重新登录';
        case 403:
          return '权限不足，无法执行此操作';
        case 404:
          return '请求的内容不存在';
        case 500:
        case 502:
        case 503:
          return '服务暂时不可用，请稍后重试';
        default:
          return _mapStringError(msg);
      }
    } catch (e) {
      return '操作失败，请稍后重试';
    }
  }

  /// 映射通用异常
  static String _mapGeneralException(Exception error) {
    final errorString = error.toString();
    return _mapStringError(errorString);
  }

  /// 检查字符串是否包含中文
  static bool _containsChinese(String text) {
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// 检查网络连接状态
  static Future<bool> isNetworkAvailable() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      return true; // 如果检查失败，假设网络可用
    }
  }

  /// 获取网络状态相关的错误消息
  static Future<String> getNetworkAwareErrorMessage(dynamic error) async {
    // 尝试使用NetworkService获取网络状态
    try {
      if (Get.isRegistered<NetworkService>()) {
        final networkService = NetworkService.to;
        if (!networkService.isConnected) {
          return networkService.getNetworkErrorMessage();
        }
      }
    } catch (e) {
      // 如果NetworkService不可用，回退到基础检查
      final isConnected = await isNetworkAvailable();
      if (!isConnected) {
        return '网络连接不可用，请检查网络设置';
      }
    }

    return getUserFriendlyMessage(error);
  }
}
