import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:flutter/foundation.dart';

import '../error_handler.dart';
import 'api_response.dart';
import 'http_exceptions.dart';
import 'http_interceptors.dart';

/// HTTP客户端配置
class HttpClientConfig {
  /// 基础URL
  final String? baseUrl;

  /// 连接超时时间
  final Duration connectTimeout;

  /// 发送超时时间
  final Duration sendTimeout;

  /// 接收超时时间
  final Duration receiveTimeout;

  /// 默认headers
  final Map<String, dynamic>? headers;

  /// 代理设置
  final String? proxy;

  /// 是否启用缓存
  final bool enableCache;

  /// 缓存配置
  final CacheConfig cacheConfig;

  /// 是否启用重试
  final bool enableRetry;

  /// 最大重试次数
  final int maxRetries;

  const HttpClientConfig({
    this.baseUrl,
    this.connectTimeout = const Duration(seconds: 10),
    this.sendTimeout = const Duration(seconds: 10),
    this.receiveTimeout = const Duration(seconds: 30),
    this.headers,
    this.proxy,
    this.enableCache = true,
    this.cacheConfig = const CacheConfig(),
    this.enableRetry = true,
    this.maxRetries = 3,
  });
}

/// 统一的HTTP客户端
///
/// 提供统一的网络请求接口，支持拦截器、缓存、重试等功能
class HttpClient {
  Dio? _dio;
  static HttpClient? _instance;

  HttpClient._internal();

  /// 获取单例实例
  static HttpClient get instance {
    _instance ??= HttpClient._internal();
    return _instance!;
  }

  /// 重置实例（主要用于测试）
  static void resetInstance() {
    _instance = null;
  }

  /// 初始化HTTP客户端
  void init(HttpClientConfig config) {
    final options = BaseOptions(
      baseUrl: config.baseUrl ?? '',
      connectTimeout: config.connectTimeout,
      sendTimeout: config.sendTimeout,
      receiveTimeout: config.receiveTimeout,
      headers: config.headers,
    );

    _dio = Dio(options);

    // 添加通用拦截器
    _dio!.interceptors.add(CommonInterceptor());

    // 添加日志拦截器（仅在调试模式下）
    if (kDebugMode) {
      _dio!.interceptors.add(LoggingInterceptor());
    }

    // 添加重试拦截器
    if (config.enableRetry) {
      _dio!.interceptors.add(RetryInterceptor(maxRetries: config.maxRetries));
    }

    // 添加缓存拦截器
    if (config.enableCache) {
      final cacheOptions = CacheOptions(
        store: MemCacheStore(),
        policy: CachePolicy.request,
        hitCacheOnErrorExcept: [401, 403],
        maxStale: config.cacheConfig.maxAge,
        priority: CachePriority.normal,
        cipher: null,
        keyBuilder: CacheOptions.defaultCacheKeyBuilder,
        allowPostMethod: false,
      );
      _dio!.interceptors.add(DioCacheInterceptor(options: cacheOptions));
    }

    // 添加错误处理拦截器
    _dio!.interceptors.add(ErrorHandlingInterceptor());

    // 设置HTTP客户端适配器
    _dio!.httpClientAdapter = IOHttpClientAdapter();

    // 设置代理
    if (config.proxy?.isNotEmpty == true) {
      _setProxy(config.proxy!);
    }
  }

  /// 设置代理
  void _setProxy(String proxy) {
    (_dio!.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
        (client) {
      client.findProxy = (uri) => "PROXY $proxy";
      client.badCertificateCallback = (cert, host, port) => true;
      return client;
    };
  }

  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio!.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    T Function(dynamic)? parser,
  }) async {
    try {
      print('🌐 HttpClient POST 请求详情:');
      print('  - 路径: $path');
      print('  - 完整URL: ${_dio!.options.baseUrl}$path');
      print('  - 请求数据: $data');
      print('  - 查询参数: $queryParameters');
      print('  - 请求头: ${options?.headers ?? _dio!.options.headers}');

      final response = await _dio!.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );

      print('📡 HttpClient POST 响应详情:');
      print('  - 状态码: ${response.statusCode}');
      print('  - 状态消息: ${response.statusMessage}');
      print('  - 响应头: ${response.headers}');
      print('  - 响应数据类型: ${response.data.runtimeType}');
      print('  - 响应数据: ${response.data}');

      return _handleResponse<T>(response, parser);
    } catch (e) {
      print('💥 HttpClient POST 请求异常: $e');
      if (e is DioException) {
        print('  - 异常类型: ${e.type}');
        print('  - 异常消息: ${e.message}');
        print('  - 响应状态码: ${e.response?.statusCode}');
        print('  - 响应数据: ${e.response?.data}');
      }
      return _handleError<T>(e);
    }
  }

  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio!.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _dio!.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return _handleResponse<T>(response, parser);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// 处理响应
  ApiResponse<T> _handleResponse<T>(
      Response response, T Function(dynamic)? parser) {
    try {
      print('🔍 HttpClient 响应处理详情:');
      print('  - HTTP状态码: ${response.statusCode}');
      print('  - HTTP状态消息: ${response.statusMessage}');

      // 检查HTTP状态码
      if (response.statusCode == null ||
          response.statusCode! < 200 ||
          response.statusCode! >= 300) {
        print('❌ HTTP状态码错误: ${response.statusCode}');
        return ApiResponse.failure(
          message: response.statusMessage ?? '请求失败',
          errorCode: response.statusCode,
          statusCode: response.statusCode,
        );
      }

      // 解析响应数据
      final responseData = response.data;
      print('  - 响应数据类型: ${responseData.runtimeType}');
      print('  - 响应数据内容: $responseData');

      // 检查业务状态码
      if (responseData is Map<String, dynamic>) {
        final code = responseData['code'] ?? responseData['status'];
        final message = responseData['msg'] ?? responseData['message'];
        final data = responseData['data'];

        print('  - 业务状态码: $code');
        print('  - 业务消息: $message');
        print('  - 业务数据类型: ${data.runtimeType}');
        print('  - 业务数据内容: $data');

        // 根据业务状态码判断成功或失败
        if (code == 1 || code == 100 || code == 200) {
          print('✅ 业务处理成功');
          final parsedData = parser != null ? parser(data) : data as T;
          print('  - 解析后数据类型: ${parsedData.runtimeType}');
          return ApiResponse.success(parsedData,
              statusCode: response.statusCode);
        } else {
          print('❌ 业务处理失败: code=$code, message=$message');
          return ApiResponse.failure(
            message: message?.toString() ?? '业务处理失败',
            errorCode: code,
            statusCode: response.statusCode,
          );
        }
      } else {
        print('📦 直接返回响应数据');
        // 直接返回响应数据
        final parsedData =
            parser != null ? parser(responseData) : responseData as T;
        return ApiResponse.success(parsedData, statusCode: response.statusCode);
      }
    } catch (e) {
      final userFriendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      return ApiResponse.failure(
        message: userFriendlyMessage,
        errorCode: -100,
        statusCode: response.statusCode,
      );
    }
  }

  /// 处理错误
  ApiResponse<T> _handleError<T>(dynamic error) {
    print('💥 HttpClient 错误处理详情:');
    print('  - 错误类型: ${error.runtimeType}');
    print('  - 错误内容: $error');

    // 使用统一的错误处理器获取用户友好的错误消息
    final userFriendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
    print('  - 用户友好消息: $userFriendlyMessage');

    if (error is DioException) {
      print('  - Dio异常类型: ${error.type}');
      print('  - Dio异常消息: ${error.message}');
      print('  - 响应状态码: ${error.response?.statusCode}');
      print('  - 响应数据: ${error.response?.data}');
      print('  - 请求路径: ${error.requestOptions.path}');
      print('  - 请求数据: ${error.requestOptions.data}');

      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          print('⏰ 请求超时');
          return ApiResponse.timeout(userFriendlyMessage);
        case DioExceptionType.cancel:
          print('🚫 请求被取消');
          return ApiResponse.cancelled(userFriendlyMessage);
        case DioExceptionType.badResponse:
          print('📛 响应错误');
          final statusCode = error.response?.statusCode;
          final responseData = error.response?.data;
          print('  - 错误状态码: $statusCode');
          print('  - 错误响应数据: $responseData');

          return ApiResponse.failure(
            message: userFriendlyMessage,
            errorCode: statusCode,
            statusCode: statusCode,
          );
        case DioExceptionType.unknown:
        default:
          print('🌐 网络错误');
          return ApiResponse.networkError(userFriendlyMessage);
      }
    } else if (error is HttpException) {
      print('🔗 HTTP异常');
      return ApiResponse.failure(
        message: userFriendlyMessage,
        errorCode: error.code,
      );
    } else {
      print('❓ 未知错误');
      return ApiResponse.unknown(userFriendlyMessage);
    }
  }

  /// 获取原始Dio实例（用于特殊需求）
  Dio get dio => _dio!;
}
