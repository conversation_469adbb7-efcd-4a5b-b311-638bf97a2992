import '../error_handler.dart';

/// API响应统一封装类
///
/// 提供统一的响应格式，包含成功/失败状态、数据载荷和错误信息
class ApiResponse<T> {
  /// 请求是否成功
  final bool isSuccess;
  
  /// 响应数据
  final T? data;
  
  /// 错误消息
  final String? message;
  
  /// 错误代码
  final int? errorCode;
  
  /// HTTP状态码
  final int? statusCode;

  const ApiResponse._({
    required this.isSuccess,
    this.data,
    this.message,
    this.errorCode,
    this.statusCode,
  });

  /// 创建成功响应
  factory ApiResponse.success(T data, {int? statusCode}) {
    return ApiResponse._(
      isSuccess: true,
      data: data,
      statusCode: statusCode,
    );
  }

  /// 创建失败响应
  factory ApiResponse.failure({
    String? message,
    int? errorCode,
    int? statusCode,
  }) {
    return ApiResponse._(
      isSuccess: false,
      message: ErrorHandler.getUserFriendlyMessage(message ?? '请求失败'),
      errorCode: errorCode,
      statusCode: statusCode,
    );
  }

  /// 创建网络错误响应
  factory ApiResponse.networkError([String? message]) {
    return ApiResponse._(
      isSuccess: false,
      message: ErrorHandler.getUserFriendlyMessage(message ?? 'network error'),
      errorCode: -1,
    );
  }

  /// 创建超时错误响应
  factory ApiResponse.timeout([String? message]) {
    return ApiResponse._(
      isSuccess: false,
      message: ErrorHandler.getUserFriendlyMessage(message ?? 'timeout'),
      errorCode: -2,
    );
  }

  /// 创建取消错误响应
  factory ApiResponse.cancelled([String? message]) {
    return ApiResponse._(
      isSuccess: false,
      message: ErrorHandler.getUserFriendlyMessage(message ?? 'cancelled'),
      errorCode: -3,
    );
  }

  /// 创建未知错误响应
  factory ApiResponse.unknown([String? message]) {
    return ApiResponse._(
      isSuccess: false,
      message: ErrorHandler.getUserFriendlyMessage(message ?? 'unknown error'),
      errorCode: -999,
    );
  }

  /// 是否为失败响应
  bool get isFailure => !isSuccess;

  /// 获取数据，如果失败则抛出异常
  T get requireData {
    if (isFailure) {
      throw Exception(message ?? '请求失败');
    }
    return data!;
  }

  /// 转换数据类型
  ApiResponse<R> map<R>(R Function(T data) mapper) {
    if (isFailure) {
      return ApiResponse.failure(
        message: message,
        errorCode: errorCode,
        statusCode: statusCode,
      );
    }
    
    try {
      final mappedData = mapper(data!);
      return ApiResponse.success(mappedData, statusCode: statusCode);
    } catch (e) {
      return ApiResponse.failure(
        message: '数据转换失败: ${e.toString()}',
        errorCode: -100,
        statusCode: statusCode,
      );
    }
  }

  /// 异步转换数据类型
  Future<ApiResponse<R>> mapAsync<R>(Future<R> Function(T data) mapper) async {
    if (isFailure) {
      return ApiResponse.failure(
        message: message,
        errorCode: errorCode,
        statusCode: statusCode,
      );
    }
    
    try {
      final mappedData = await mapper(data!);
      return ApiResponse.success(mappedData, statusCode: statusCode);
    } catch (e) {
      return ApiResponse.failure(
        message: '数据转换失败: ${e.toString()}',
        errorCode: -100,
        statusCode: statusCode,
      );
    }
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'ApiResponse.success(data: $data, statusCode: $statusCode)';
    } else {
      return 'ApiResponse.failure(message: $message, errorCode: $errorCode, statusCode: $statusCode)';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ApiResponse<T> &&
        other.isSuccess == isSuccess &&
        other.data == data &&
        other.message == message &&
        other.errorCode == errorCode &&
        other.statusCode == statusCode;
  }

  @override
  int get hashCode {
    return isSuccess.hashCode ^
        data.hashCode ^
        message.hashCode ^
        errorCode.hashCode ^
        statusCode.hashCode;
  }
}
