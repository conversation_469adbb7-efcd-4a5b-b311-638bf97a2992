import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

/// 网络状态检测服务
///
/// 提供网络连接状态检测和监听功能
class NetworkService extends GetxService {
  static NetworkService get to => Get.find();

  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  /// 当前网络连接状态
  final RxBool _isConnected = true.obs;
  bool get isConnected => _isConnected.value;

  /// 当前连接类型
  final Rx<ConnectivityResult> _connectionType = ConnectivityResult.none.obs;
  ConnectivityResult get connectionType => _connectionType.value;

  /// 网络状态变化回调
  final List<Function(bool)> _listeners = [];

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initNetworkStatus();
    _startNetworkMonitoring();
  }

  @override
  void onClose() {
    _connectivitySubscription.cancel();
    super.onClose();
  }

  /// 初始化网络状态
  Future<void> _initNetworkStatus() async {
    try {
      final result = await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      print('初始化网络状态失败: $e');
      _isConnected.value = true; // 默认假设有网络
    }
  }

  /// 开始监听网络状态变化
  void _startNetworkMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
      onError: (error) {
        print('网络状态监听错误: $error');
      },
    );
  }

  /// 更新连接状态
  Future<void> _updateConnectionStatus(List<ConnectivityResult> results) async {
    // 取第一个连接类型作为主要连接类型
    final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
    _connectionType.value = result;

    // 检查实际网络连接
    final hasInternet = await _checkInternetConnection();
    final wasConnected = _isConnected.value;
    _isConnected.value = hasInternet;

    // 如果状态发生变化，通知监听者
    if (wasConnected != hasInternet) {
      _notifyListeners(hasInternet);
    }

    print('网络状态更新: ${_getConnectionTypeName(result)}, 实际连接: $hasInternet');
  }

  /// 检查实际的网络连接
  Future<bool> _checkInternetConnection() async {
    try {
      // 尝试连接到可靠的服务器
      final result = await InternetAddress.lookup('www.baidu.com')
          .timeout(const Duration(seconds: 5));
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 获取连接类型名称
  String _getConnectionTypeName(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return '移动网络';
      case ConnectivityResult.ethernet:
        return '以太网';
      case ConnectivityResult.bluetooth:
        return '蓝牙';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return '其他';
      case ConnectivityResult.none:
      default:
        return '无连接';
    }
  }

  /// 添加网络状态监听器
  void addListener(Function(bool) listener) {
    _listeners.add(listener);
  }

  /// 移除网络状态监听器
  void removeListener(Function(bool) listener) {
    _listeners.remove(listener);
  }

  /// 通知所有监听者
  void _notifyListeners(bool isConnected) {
    for (final listener in _listeners) {
      try {
        listener(isConnected);
      } catch (e) {
        print('网络状态监听器执行错误: $e');
      }
    }
  }

  /// 手动检查网络状态
  Future<bool> checkNetworkStatus() async {
    final result = await _connectivity.checkConnectivity();
    await _updateConnectionStatus(result);
    return _isConnected.value;
  }

  /// 获取网络状态描述
  String getNetworkStatusDescription() {
    if (!_isConnected.value) {
      return '网络连接不可用';
    }

    return '已连接到${_getConnectionTypeName(_connectionType.value)}';
  }

  /// 是否为移动网络
  bool get isMobileNetwork =>
      _connectionType.value == ConnectivityResult.mobile;

  /// 是否为WiFi网络
  bool get isWiFiNetwork => _connectionType.value == ConnectivityResult.wifi;

  /// 获取用户友好的网络错误消息
  String getNetworkErrorMessage() {
    if (!_isConnected.value) {
      return '网络连接不可用，请检查网络设置后重试';
    }

    switch (_connectionType.value) {
      case ConnectivityResult.wifi:
        return '网络连接不稳定，请检查WiFi连接';
      case ConnectivityResult.mobile:
        return '移动网络连接不稳定，请检查信号强度';
      default:
        return '网络连接异常，请稍后重试';
    }
  }
}
