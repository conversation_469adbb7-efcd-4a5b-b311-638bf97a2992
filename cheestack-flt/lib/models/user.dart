part of models;

class UsrModel {
  UsrModel({this.user, this.auth, this.config});

  UserModel? user;
  AuthModel? auth;
  ConfigModel? config;

  @override
  String toString() => 'UserData(user: $user, auth: $auth, config: $config)';

  factory UsrModel.fromJson(Map<String, Object?> json) => UsrModel(
        user: json['user'] == null
            ? null
            : UserModel.fromJson(json['user']! as Map<String, Object?>),
        auth: json['auth'] == null
            ? null
            : AuthModel.fromJson(json['auth']! as Map<String, Object?>),
        config: json['config'] == null
            ? null
            : ConfigModel.fromJson(json['config']! as Map<String, Object?>),
      );

  Map<String, Object?> toJson() => {
        'user': user?.toJson(),
        'auth': auth?.toJson(),
        'config': config?.toJson(),
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! UsrModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => user.hashCode ^ auth.hashCode ^ config.hashCode;
}

class LoginModel {
  String? username;
  String? password;
  String? mobile;
  String? captcha;
  bool? easyMode;

  LoginModel({
    this.username,
    this.password,
    this.mobile,
    this.captcha,
    this.easyMode,
  });

  LoginModel.fromJson(Map<String, dynamic> json) {
    if (json["username"] is String) {
      username = json["username"];
    }
    if (json["password"] is String) {
      password = json["password"];
    }
    if (json["mobile"] is String) {
      mobile = json["mobile"];
    }
    if (json["captcha"] is String) {
      captcha = json["captcha"];
    }
    if (json["easy_mode"] is bool) {
      easyMode = json["easy_mode"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["username"] = username;
    data["password"] = password;
    data["mobile"] = mobile;
    data["captcha"] = captcha;
    data["easy_mode"] = easyMode;
    return data;
  }

  LoginModel copyWith({
    String? username,
    String? password,
    String? mobile,
    String? captcha,
    bool? easyMode,
  }) =>
      LoginModel(
        username: username ?? this.username,
        password: password ?? this.password,
        mobile: mobile ?? this.mobile,
        captcha: captcha ?? this.captcha,
        easyMode: easyMode ?? this.easyMode,
      );
}

class CosTokenModel {
  String? tmpSecretId;
  String? tmpSecretKey;
  String? sessionToken;
  int? startTime;
  int? expiredTime;
  String? bucket;
  String? region;
  String? filePath;

  CosTokenModel(
      {this.tmpSecretId,
      this.tmpSecretKey,
      this.sessionToken,
      this.startTime,
      this.expiredTime,
      this.bucket,
      this.region,
      this.filePath});

  CosTokenModel.fromJson(Map<String, dynamic> json) {
    if (json["tmp_secret_id"] is String) {
      tmpSecretId = json["tmp_secret_id"];
    }
    if (json["tmp_secret_key"] is String) {
      tmpSecretKey = json["tmp_secret_key"];
    }
    if (json["session_token"] is String) {
      sessionToken = json["session_token"];
    }
    if (json["start_time"] is int) {
      startTime = json["start_time"];
    }
    if (json["expired_time"] is int) {
      expiredTime = json["expired_time"];
    }
    if (json["bucket"] is String) {
      bucket = json["bucket"];
    }
    if (json["region"] is String) {
      region = json["region"];
    }
    if (json["file_path"] is String) {
      filePath = json["file_path"];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["tmp_secret_id"] = tmpSecretId;
    data["tmp_secret_key"] = tmpSecretKey;
    data["session_token"] = sessionToken;
    data["start_time"] = startTime;
    data["expired_time"] = expiredTime;
    data["bucket"] = bucket;
    data["region"] = region;
    data["file_path"] = filePath;
    return data;
  }

  CosTokenModel copyWith({
    String? tmpSecretId,
    String? tmpSecretKey,
    String? sessionToken,
    int? startTime,
    int? expiredTime,
    String? bucket,
    String? region,
    String? filePath,
  }) =>
      CosTokenModel(
        tmpSecretId: tmpSecretId ?? this.tmpSecretId,
        tmpSecretKey: tmpSecretKey ?? this.tmpSecretKey,
        sessionToken: sessionToken ?? this.sessionToken,
        startTime: startTime ?? this.startTime,
        expiredTime: expiredTime ?? this.expiredTime,
        bucket: bucket ?? this.bucket,
        region: region ?? this.region,
        filePath: filePath ?? this.filePath,
      );
}

class UserModel {
  UserModel({
    this.createdAt,
    this.updatedAt,
    this.id,
    this.username,
    this.mobile,
    this.email,
    this.avatar,
    this.intro,
    this.config,
    this.token,
  });

  DateTime? createdAt;
  DateTime? updatedAt;
  String? id;
  String? username;
  String? mobile;
  dynamic email;
  String? avatar;
  String? intro;
  ConfigModel? config;
  String? token;

  @override
  String toString() {
    return 'UserModel(createdAt: $createdAt, updatedAt: $updatedAt, id: $id, username: $username, mobile: $mobile, email: $email, avatar: $avatar, intro: $intro, config: $config, token: $token)';
  }

  factory UserModel.fromJson(Map<String, Object?> json) => UserModel(
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']!.toString()),
        updatedAt: json['updated_at'] == null
            ? null
            : DateTime.parse(json['updated_at']!.toString()),
        id: json['id']?.toString(),
        username: json['username']?.toString(),
        mobile: json['mobile']?.toString(),
        email: json['email']?.toString(),
        avatar: json['avatar']?.toString(),
        intro: json['intro']?.toString(),
        config: json['config'] == null
            ? null
            : ConfigModel.fromJson(json['config']! as Map<String, Object?>),
        token: json['token'] as String?,
      );

  Map<String, Object?> toJson() => {
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'id': id,
        'username': username,
        'mobile': mobile,
        'email': email,
        'avatar': avatar,
        'intro': intro,
        'config': config?.toJson(),
        'token': token,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! UserModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      createdAt.hashCode ^
      updatedAt.hashCode ^
      id.hashCode ^
      username.hashCode ^
      mobile.hashCode ^
      email.hashCode ^
      avatar.hashCode ^
      intro.hashCode ^
      config.hashCode ^
      token.hashCode;

  /// 创建一个新的UserModel实例，但可以更新部分属性
  UserModel copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    String? id,
    String? username,
    String? mobile,
    dynamic email,
    String? avatar,
    String? intro,
    ConfigModel? config,
    String? token,
  }) {
    return UserModel(
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      id: id ?? this.id,
      username: username ?? this.username,
      mobile: mobile ?? this.mobile,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      intro: intro ?? this.intro,
      config: config ?? this.config,
      token: token ?? this.token,
    );
  }
}

class AuthModel {
  AuthModel({this.id, this.accessToken, this.refreshToken});

  String? id;
  String? accessToken;
  String? refreshToken;

  @override
  String toString() {
    return 'AuthModel(id: $id, accessToken: $accessToken, refreshToken: $refreshToken)';
  }

  factory AuthModel.fromJson(Map<String, Object?> json) => AuthModel(
        id: json['id'] as String?,
        accessToken: json['access_token'] as String?,
        refreshToken: json['refresh_token'] as String?,
      );

  Map<String, Object?> toJson() => {
        'id': id,
        'access_token': accessToken,
        'refresh_token': refreshToken,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! AuthModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^ accessToken.hashCode ^ refreshToken.hashCode;
}

class ConfigModel {
  ConfigModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.isAutoPlayAudio,
    this.isAutoPlayAiAudio,
    this.reviewNumber,
    this.studyNumber,
    this.studyType,
    this.currentStudyId,
    this.editingBook,
    this.editingBookId,
  });

  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  bool? isAutoPlayAudio;
  bool? isAutoPlayAiAudio;
  int? reviewNumber;
  int? studyNumber;
  int? studyType;
  int? currentStudyId;
  EditingBook? editingBook;
  int? editingBookId;

  @override
  String toString() {
    return 'Config(id: $id, createdAt: $createdAt, updatedAt: $updatedAt, isAutoPlayAudio: $isAutoPlayAudio, isAutoPlayAiAudio: $isAutoPlayAiAudio, reviewNumber: $reviewNumber, studyNumber: $studyNumber, studyType: $studyType, currentStudyId: $currentStudyId, editingBook: $editingBook, editingBookId: $editingBookId)';
  }

  factory ConfigModel.fromJson(Map<String, Object?> json) => ConfigModel(
        id: json['id'] as int?,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']!.toString()),
        updatedAt: json['updated_at'] == null
            ? null
            : DateTime.parse(json['updated_at']!.toString()),
        isAutoPlayAudio: json['is_auto_play_audio'] as bool?,
        isAutoPlayAiAudio: json['is_auto_play_ai_audio'] as bool?,
        reviewNumber: json['review_number'] as int?,
        studyNumber: json['study_number'] as int?,
        studyType: json['study_type'] as int?,
        currentStudyId: json['current_study_id'] as int?,
        editingBook: json['editing_book'] == null
            ? null
            : EditingBook.fromJson(
                json['editing_book']! as Map<String, Object?>),
        editingBookId: json['editing_book_id'] as int?,
      );

  Map<String, Object?> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'is_auto_play_audio': isAutoPlayAudio,
        'is_auto_play_ai_audio': isAutoPlayAiAudio,
        'review_number': reviewNumber,
        'study_number': studyNumber,
        'study_type': studyType,
        'current_study_id': currentStudyId,
        'editing_book': editingBook?.toJson(),
        'editing_book_id': editingBookId,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! ConfigModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      isAutoPlayAudio.hashCode ^
      isAutoPlayAiAudio.hashCode ^
      reviewNumber.hashCode ^
      studyNumber.hashCode ^
      studyType.hashCode ^
      currentStudyId.hashCode ^
      editingBook.hashCode ^
      editingBookId.hashCode;

  /// 创建一个新的ConfigModel实例，但可以更新部分属性
  ConfigModel copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isAutoPlayAudio,
    bool? isAutoPlayAiAudio,
    int? reviewNumber,
    int? studyNumber,
    int? studyType,
    int? currentStudyId,
    EditingBook? editingBook,
    int? editingBookId,
  }) {
    return ConfigModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isAutoPlayAudio: isAutoPlayAudio ?? this.isAutoPlayAudio,
      isAutoPlayAiAudio: isAutoPlayAiAudio ?? this.isAutoPlayAiAudio,
      reviewNumber: reviewNumber ?? this.reviewNumber,
      studyNumber: studyNumber ?? this.studyNumber,
      studyType: studyType ?? this.studyType,
      currentStudyId: currentStudyId ?? this.currentStudyId,
      editingBook: editingBook ?? this.editingBook,
      editingBookId: editingBookId ?? this.editingBookId,
    );
  }
}

class EditingBook {
  EditingBook({this.id, this.name});

  int? id;
  String? name;

  @override
  String toString() => 'EditingBook(id: $id, name: $name)';

  factory EditingBook.fromJson(Map<String, Object?> json) => EditingBook(
        id: json['id'] as int?,
        name: json['name'] as String?,
      );

  Map<String, Object?> toJson() => {
        'id': id,
        'name': name,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! EditingBook) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}

class UserInModel {
  UserInModel({this.username, this.intro, this.avatar});

  String? username;
  String? intro;
  String? avatar;

  @override
  String toString() {
    return 'UserInModel(username: $username, intro: $intro, avatar: $avatar)';
  }

  factory UserInModel.fromJson(Map<String, Object?> json) => UserInModel(
        username: json['username'] as String?,
        intro: json['intro'] as String?,
        avatar: json['avatar'] as String?,
      );

  Map<String, Object?> toJson() => {
        'username': username,
        'intro': intro,
        'avatar': avatar,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! UserInModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => username.hashCode ^ intro.hashCode ^ avatar.hashCode;
}
