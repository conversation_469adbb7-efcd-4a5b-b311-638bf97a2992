part of widgets;

/// 简化的同步状态监控组件
class SyncStatusWidget extends StatelessWidget {
  final bool showManualSync;
  final VoidCallback? onManualSync;
  
  const SyncStatusWidget({
    Key? key,
    this.showManualSync = true,
    this.onManualSync,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
        ),
      ),
      child: const OxText(
        '数据同步正常',
        fontSize: 12.0,
        color: Colors.blue,
      ),
    );
  }
}

/// 简化的同步状态指示器
class SimpleSyncIndicator extends StatelessWidget {
  const SimpleSyncIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Icon(
      Icons.sync,
      size: 16,
      color: Colors.blue,
    );
  }
}
