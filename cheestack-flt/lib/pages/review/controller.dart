part of review;

class ReviewController extends LoadStateController {
  PageController pageController = PageController();

  ReviewController();

  List<Widget> pageViewList = [];
  // late JustAudioController player;
  /// define `pageIndex`
  int pageIndex = 0;
  int maxPageIndex = 2;

  bool isCorrect = true;
  Key adkey = UniqueKey();

  /// 定义`reviewType`
  StudyType studyType = StudyType.review;

  // ReviewListCopy
  List<CardModel> cardListCopy = [];

  /// define `reviewModelList`
  List<ScheduleModel> reviewModelList = [];

  /// define `cardModelList`
  List<CardModel> cardModelList = [];

  /// define `qty` as int
  int qty = 0;
  // 复习记录列表
  List<RecordModel> records = [];

  /// Current Card
  CardModel curCard = CardModel();

  /// current card options
  List<OptionModel> options = [];
  bool scheduleChange = false;
  List<PronunciationModel> pronunciationList = [];
  List<String> unmatch = [];
  int? bookId;

  @override
  void onInit() {
    initData();
    super.onInit();
  }

  @override
  void onReady() async {
    // 停止磨耳朵的听力
    stopListening();
    await getPerssion();
    await checkUndoTask();

    /// 远程获取各种数据和初始化各种实例
    // await initData();
    super.onReady();
  }

  void stopListening() {
    // 只有存在controller才执行停止操作
    if (Get.isRegistered<ChvAudioController>()) {
      ChvAudioController.to.stop();
    }
  }

  @override
  void onClose() async {
    // 释放内存
    await OxAudioController.to.stop();
    await TTSUtil().stop();
    super.onClose();
  }

  /// 处理复习结果
  onResulHandle(
    bool isCorrect, {
    String? correctAnswer,
    String? responese,
  }) async {
    /// 预览模式
    if (studyType == StudyType.info) {
      Get.back();
      return;
    }

    /// 如果卡片未提交过结果, 远程提交记录本次复习结果
    if (curCard.isReviewd == false) {
      RecordModel record =
          RecordModel(cardId: curCard.id, isCorrect: isCorrect);
      records.add(record);
      postRecords();
    }
    // 首先移除并获得当前卡片, 后面根据情况判断是否加强复习
    cardModelList.removeAt(0);
    Console.log("isCorrect: $isCorrect");
    // 如果回答错误且剩余列表元素大于1,插入后面随机位置再复习一次
    if (!isCorrect && cardModelList.isNotEmpty) reviewAgain();
    if (cardModelList.isNotEmpty) {
      // playResultAudio(isCorrect);
      // 更新当前卡片
      prepareCards();
    } else {
      // 如果没有复习数据了, 返回上一页
      ShowToast.success("恭喜! 你已完成本次复习!");
      Future.delayed(const Duration(seconds: 1), getBack);
    }
  }

  /// fetch data from server
  Future getReviewList() async {
    cardModelList = await ApiReview.list();
    await asyncRequest(
      () =>
          OxHttp.to
          .get(HttpUrl.studies, queryParameters: {"book_id": bookId}),
      onSuccess: (data) async {
        if (data != null) {
          cardModelList =
              data.map<CardModel>((x) => CardModel.fromJson(x)).toList();
          cardListCopy = List.from(cardModelList);
          // await getPronunciations();
          // 更新当前的卡片信息
          await prepareCards();
          cardModelList.isNotEmpty ? loadSuccess() : loadEmpty();
          update();
        }
      },
      onFailure: (error) {
        ShowToast.fail(error?.msg ?? "未知错误");
        loadFailed();
      },
    );
  }

  Future getStudyList() async {
    await asyncRequest(
      () => OxHttp.to.get(
        HttpUrl.myStudyCards,
        queryParameters: {"qty": qty},
      ),
      onSuccess: (data) async {
        // 更新当前的卡片信息
        cardModelList =
            data.map<CardModel>((e) => CardModel.fromJson(e)).toList();
        cardListCopy = List.from(cardModelList);
        // 更新当前的卡片信息
        await prepareCards();
        cardModelList.isNotEmpty ? loadSuccess() : loadEmpty();
        update();
      },
      onFailure: (error) {
        ShowToast.fail(error?.msg ?? "未知错误");
      },
    );
  }

  void toggleStudy() {
    scheduleChange = true;
    if (curCard.scheduleId == null) {
      Console.log("加入复习");
      postSchedule({
        "card_id": curCard.id,
      });
    } else {
      Console.log("取消复习");
      Sdialog.show(
        builder: (BuildContext context) {
          return const OxText("该操作会删除学习记录");
        },
        onConfirm: () async {
          Get.back();
          deleteSchedule(curCard.scheduleId);
        },
        onCancel: () => Get.back(),
      );
    }
    update();
  }

  /// 添加复习记录
  Future postSchedule(Map<String, dynamic> data) async {
    ShowToast.loading();
    await asyncRequest(
      () => OxHttp.to.post(
        HttpUrl.schedules,
        data: data,
      ),
      onSuccess: (data) {
        // dataModel = Sche.fromJson(data);
        curCard.scheduleId = data['id'];
        update();
      },
      onFailure: (error) {
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
        ShowToast.fail(friendlyMessage);
      },
    );
    ShowToast.dismiss();
  }

  /// 删除复习记录
  Future deleteSchedule(int? id) async {
    ShowToast.loading();
    await asyncRequest(
      () => OxHttp.to.delete(
        "${HttpUrl.schedules}/$id",
      ),
      onSuccess: (data) {
        curCard.scheduleId = null;
        update();
      },
      onFailure: (error) {
        ShowToast.fail(error?.msg ?? "未知错误");
      },
    );
    ShowToast.dismiss();
  }

  /// 更新当前的笔记数据
  /// @param card 如果传入了card, 则直接使用传入的card, 否则使用`reviewList`的第一个元素
  /// @return void
  Future<void> prepareCards() async {
    /// 设置列表中的第一个为当前卡片
    if (cardModelList.isEmpty) return;

    // 重置状态
    curCard = cardModelList.firstNullable ?? CardModel();

    unmatch = [];
    isCorrect = true;
    switch (curCard.type?.cardType) {
      case CardType.languageGeneral:
        maxPageIndex = 2;
        handleOptionData(CardType.languageGeneral);
        await getPronunciations();
        break;
      default:
        maxPageIndex = 1;
        options = [];
    }

    /// 异步获取数据
    cacheAudioFiles();
    update();
  }

  Future cacheAudioFiles() async {
    List<CardAsset> cardAssets = [];
    // List<String> words = [];
    List<String?> audioUrls = [];

    try {
      // int length = cardModelList.length >= 2 ? 2 : cardModelList.length;
      // for (int i = 0; i < length; i++) {
      //   cardAssets.addAll(cardModelList[i].cardAssets ?? []);
      //   words.addAll(cardModelList[i]
      //           .question
      //           ?.replacePunctuationWithSpace()
      //           .toLowerCase()
      //           .split(RegExp(r'\s+')) ??
      //       []);
      // }

      /// 如果没有`assets`字段，则直接返回
      if (cardAssets.isEmpty) return;

      /// 整理句子的音频
      audioUrls.addAll(cardAssets
          .filter((e) => (e.type ?? "").contains("audio"))
          .map((e) => e.url)
          .toList());

      /// 整理单词的音频
      // audioUrls.addAll(words.map((e) {
      //   return pronunciationList.firstWhere((x) => x.name == e).url ?? "";
      // }).toList());

      /// 缓存音频
      if (audioUrls.isNotEmpty) {
        for (String? url in audioUrls) {
          if (url.isNotNullOrEmpty) {
            await DefaultCacheManager().getSingleFile(url!);
          }
        }
      }

      /// 缓存单词的图片
      // cardAssets.filter((e) => (e.type ?? "").contains("image")).map((e) async {
      //   if (e.url != null) {
      //     // 预缓存多张图片
      //     // await precacheImage(
      //     //   ExtendedNetworkImageProvider(e.url!, cache: true),
      //     //   Get.context!,
      //     // );
      //   }
      // });
    } catch (e) {
      Console.log(e);
    }
  }

  void handleOptionData(CardType type) {
    List<CardModel> cardList =
        cardListCopy.filter((e) => e.type == type.value).toList();
    CardAsset? cardAsset = curCard.cardAssets
        ?.firstWhereNullable((e) => e.type == CardAssetType.primaryImage.value);
    String? standardText = cardAsset?.text ?? curCard.answer;

    // 移除当前卡片
    // cardList.removeWhere((e) => e.id == curCard.id);
    List<OptionModel> allOptions = cardList.map((e) {
      CardAsset cardAsset = e.cardAssets?.firstWhereNullable(
              (t) => t.type == CardAssetType.primaryImage.value) ??
          CardAsset();
      double similarirty = StringSimilarity.compareTwoStrings(
          standardText, cardAsset.text ?? e.answer);
      return OptionModel(
        text: e.answer,
        isCorrect: e.id == curCard.id ? true : false,
        selected: false,
        imageUrl: cardAsset.url,
        similarity: similarirty,
      );
    }).toList();

    allOptions.sort((a, b) => b.similarity.compareTo(a.similarity));

    options.clear();
    options.addAll(allOptions.take(4).toList());
    options.shuffle(Random());
  }

  /// 播放结果音效
  /// @param resultCode 结果码, 根据结果码播放不同的音效
  /// @return void
  Future playResultAudio(bool isCorrect) async {
    try {
      /// 判读是否需要自动播放音频
      switch (isCorrect) {
        case false:
          // audioController.play("assets/audios/moo.mp3");
          OxAudioController.to.play("assets/audios/sadwhisle.wav");
          break;
        case true:
          await OxAudioController.to.play("assets/audios/powerup-success.wav");
          break;
        default:
      }
    } catch (e) {
      Console.log(e);
    }
  }

  Future<bool> getBack() async {
    Get.back();
    return true;
  }

  Future postRecords() async {
    await asyncRequest(
      () => OxHttp.to.post(
        HttpUrl.records,
        data: records,
      ),
      onSuccess: (data) {
        records.clear();
        StorageService.to.remove(CacheKey.recordList);
        update();
      },
      onFailure: (error) {
        StorageService.to.setJSON(CacheKey.recordList, records);
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
        ShowToast.fail(friendlyMessage);
      },
    );
  }

  void reviewAgain() {
    // 标记已复习, 并根据条件插入一个位置
    curCard.isReviewd = true; // 标记为`已复习`
    // 随机插入一个位置, 避免小孩子记住了顺序
    final random = Random();
    final index = random.nextInt(3) + 3; // 生成3~5之间的随机数
    if (index >= reviewModelList.length) {
      // 如果插入位置大于等于列表长度，则直接放在列表末尾
      cardModelList.add(curCard);
    } else {
      // 在指定位置插入数值
      cardModelList.insert(index, curCard);
    }
  }

  Future initData() async {
    // get data from Get.arguments
    Map<String, dynamic> data = Get.arguments;
    // if data is ReviewType, then assign it to reviewType
    if (data.get("type") is StudyType) {
      studyType = data.get("type");
    }

    if (data.get("bookId") is int) bookId = data.get("bookId");

    // Get the current card or card list according to the reviewType
    switch (studyType) {
      /// view
      case StudyType.info:
        cardModelList.add(data.get("cardModel"));
        cardListCopy = List.from(cardModelList);
        await prepareCards();
        loadSuccess();
        // await getWordsPronunciation();
        // 更新当前的卡片信息
        update();
        break;

      /// review
      case StudyType.review:
        if (data.get("qty") is int) qty = data.get("qty");
        await getReviewList();
        break;

      /// study
      case StudyType.study:
        if (data.get("qty") is int) qty = data.get("qty");
        // 获取card_list
        await getStudyList();
        break;
      default:
    }
  }

  toPageIndex({int plus = 1, bool isCorrect = true}) {
    pageIndex = pageIndex + plus;
    if (pageIndex < 0) pageIndex = 0;
    if (pageIndex > maxPageIndex) {
      pageIndex = 0;
      onResulHandle(isCorrect);
      pageController.jumpToPage(0);
      update();
    } else {
      if (curCard.type == CardType.general.value ||
          curCard.type == CardType.generalMarkdown.value) {
        pageController.jumpToPage(pageIndex);
      }
    }
    update();
  }

  String? getAssetUrl(
    CardAssetType type,
  ) {
    String? url = curCard.cardAssets
        ?.firstWhereNullable((e) => e.type == type.value)
        ?.url;
    return url;
  }

  Future getWhisperResult() async {
    double similarityScore = 0.0;

    String language = "en";

    if (curCard.question.isChinese) {
      language = "zh";
    }

    await asyncRequest(
      () => OxHttp.to.post(httpWhisperTranscribe,
          data: mdio.FormData.fromMap({
            "audio": mdio.MultipartFile.fromFileSync(Ssound.to.tmpAudioPath),
            "beam_size": 5,
            "language": language,
            "initial_prompt":
                "the following audio is saying: ${curCard.question ?? ""}",
            "hotwords": curCard.question,
            "standard_text": curCard.question
          })),
      onSuccess: (data) {
        ScoreModel scoreModel = ScoreModel.fromJson(data);
        similarityScore = scoreModel.score?.toDouble() ?? 1.0;
        unmatch = scoreModel.unmatch ?? [];
        update();
      },
      onFailure: (error) {
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
        ShowToast.fail(friendlyMessage.isEmpty ? "发音检测失败" : friendlyMessage);
        similarityScore = 1;
      },
    );
    return similarityScore;
  }

  /// 获取单词的发音数据
  Future getPronunciations() async {
    List<String> words = [];
    for (CardModel card in cardModelList.take(2)) {
      /// 获取CardType.languageGeneral的单词发音
      if (card.type == CardType.languageGeneral.value) {
        /// 去标点,转小写,拆分
        List<String>? wordList = card.question
            ?.replacePunctuationWithSpace()
            .toLowerCase()
            .split(RegExp(r'\s+'));
        words.addAll(wordList ?? []);
        words = words.toSet().toList(); // 去重
      }
    }

    /// 如果没有单词，直接返回
    if (words.isEmpty) return;

    /// 请求发音数据
    await asyncRequest(
      () => OxHttp.to.post(HttpUrl.ttsEnglishWord, data: words),
      onSuccess: (data) async {
        pronunciationList = data
            .map<PronunciationModel>((x) => PronunciationModel.fromJson(x))
            .toList();

        List<String> audioUrls = [];

        /// 整理单词的音频
        audioUrls.addAll(words.map((e) {
          return pronunciationList.firstWhere((x) => x.name == e).url ?? "";
        }).toList());

        /// 缓存单词的音频
        if (audioUrls.isNotEmpty) {
          for (String? url in audioUrls) {
            if (url.isNotNullOrEmpty) {
              await DefaultCacheManager().getSingleFile(url!);
            }
          }
        }
        update();
      },
      onFailure: (error) {
        pronunciationList = [];
        final friendlyMessage = ErrorHandler.getUserFriendlyMessage(error);
        Console.log(friendlyMessage);
      },
    );
  }

  Future checkUndoTask() async {
    /// 检查是否有未提交的复习记录
    List data = await StorageService.to.getJSON(CacheKey.recordList);
    if (data.isNotEmpty) {
      records = data.map<RecordModel>((x) => RecordModel.fromJson(x)).toList();
      await postRecords();
      if (records.isNotEmpty) {
        Console.log(11111);
        await Sdialog.show(
          builder: (BuildContext context) {
            return const OxText("还有本地学习记录未提交");
          },
          cancel: const OxText("放弃记录"),
          onCancel: () {
            records.clear();
            StorageService.to.remove(CacheKey.recordList);
            Get.back();
          },
          onConfirm: () {
            Get.back();
            Get.back();
          },
          confirm: const OxText("稍候重试"),
        );
      }
    }
  }

  onOptionSubmit() {
    bool isCorret = true;

    /// 如果选项数量大于1
    if (options.length > 1) {
      // 答案与选择不一致, 设置答案错误
      for (OptionModel option in options) {
        if (option.isCorrect != option.selected) {
          isCorret = false;
          break;
        }
      }
      // 清除所有选中
      for (OptionModel option in options) {
        option.selected = false;
      }

      /// options 打乱顺序
      // options.shuffle();
      update();
    }

    /// 如果答案正确，进入下一环节
    if (isCorret == true) {
      toPageIndex();
    } else {
      // playResultAudio(false);
      OxAudioController.to.play(getAssetUrl(CardAssetType.primaryAudio),
          isForceListening: true);
      // ShowToast.fail("请再试一次吧");
    }
  }

  void onPageChanged(int value) {
    pageIndex = value;

    if (curCard.type == CardType.general.value ||
        curCard.type == CardType.generalMarkdown.value) {
      if (pageIndex == 0) {
        OxAudioController.to.play(getAssetUrl(CardAssetType.primaryAudio));
      } else if (pageIndex == 1) {
        OxAudioController.to.play(getAssetUrl(CardAssetType.secondaryAudio));
      }
    }

    update();
  }

  void toFeedback() {
    Get.toNamed(AppRoutes.feedback, arguments: curCard);
  }

  Future<bool> getPerssion() async {
    // 获取设备信息

    bool micPermission = await Permission.microphone.request().isGranted;
    if (!micPermission) {
      ShowToast.fail("无法获取麦克风权限");
    }
    bool storagePermission = await Permission.storage.request().isGranted;
    if (!storagePermission) {
      storagePermission =
          await Permission.manageExternalStorage.request().isGranted;
      if (!storagePermission) {
        ShowToast.fail("无法获取存储权限");
      }
    }
    return true;
  }
}
