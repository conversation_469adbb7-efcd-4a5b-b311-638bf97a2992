# 卡片编辑器功能手动测试指南

## 测试环境准备

1. 确保应用已启动并登录
2. 确保网络连接正常
3. 确保有至少一本书籍可用于测试

## 测试用例

### 1. 书籍同步功能测试

#### 1.1 创建书籍
- [ ] 打开书籍管理页面
- [ ] 点击"添加书籍"
- [ ] 输入书籍名称："测试书籍"
- [ ] 输入书籍描述："这是一本测试书籍"
- [ ] 点击保存
- [ ] **预期结果**：书籍创建成功，显示在列表中

#### 1.2 书籍同步验证
- [ ] 检查网络请求日志，确认书籍数据已发送到服务器
- [ ] 在其他设备登录同一账号，确认书籍已同步

### 2. 卡片编辑器基础功能测试

#### 2.1 创建基础卡片
- [ ] 进入书籍详情页
- [ ] 点击"添加卡片"
- [ ] 选择卡片类型："基础卡片"
- [ ] 输入标题："测试卡片标题"
- [ ] 输入问题："这是一个测试问题？"
- [ ] 输入答案："这是测试答案"
- [ ] **预期结果**：预览页面实时显示输入内容

#### 2.2 实时预览功能
- [ ] 在编辑过程中，点击右下角的预览按钮
- [ ] **预期结果**：显示浮动预览窗口
- [ ] 修改任意输入框内容
- [ ] **预期结果**：预览窗口内容实时更新
- [ ] 点击预览窗口的关闭按钮
- [ ] **预期结果**：预览窗口关闭

#### 2.3 保存卡片
- [ ] 点击保存按钮
- [ ] **预期结果**：显示"卡片创建成功"提示
- [ ] **预期结果**：返回书籍详情页，卡片显示在列表中

### 3. 不同卡片类型测试

#### 3.1 汉字书写卡片
- [ ] 创建新卡片，选择"汉字书写"类型
- [ ] 输入要书写的汉字："学"
- [ ] 输入书写提示："学习的学"
- [ ] **预期结果**：预览显示汉字书写界面样式
- [ ] 保存卡片
- [ ] **预期结果**：卡片创建成功

#### 3.2 语言学习卡片
- [ ] 创建新卡片，选择"语言学习"类型
- [ ] 输入问题："What is the capital of China?"
- [ ] 输入正确答案："Beijing"
- [ ] **预期结果**：预览显示选择题样式，正确答案作为选项A
- [ ] 保存卡片
- [ ] **预期结果**：卡片创建成功

#### 3.3 听力练习卡片
- [ ] 创建新卡片，选择"听力练习"类型
- [ ] 输入听力问题："听音频，选择正确的单词"
- [ ] 输入正确答案："apple"
- [ ] **预期结果**：预览显示听力练习界面，提示需要上传音频
- [ ] 保存卡片
- [ ] **预期结果**：卡片创建成功

#### 3.4 朗读练习卡片
- [ ] 创建新卡片，选择"朗读练习"类型
- [ ] 输入朗读内容："Hello, how are you today?"
- [ ] 输入朗读要求："注意语调和发音"
- [ ] **预期结果**：预览显示朗读练习界面样式
- [ ] 保存卡片
- [ ] **预期结果**：卡片创建成功

#### 3.5 Markdown卡片
- [ ] 创建新卡片，选择"Markdown卡片"类型
- [ ] 输入Markdown内容：
  ```
  # 标题
  这是**粗体**文本和*斜体*文本。
  
  - 列表项1
  - 列表项2
  
  `代码示例`
  ```
- [ ] **预期结果**：预览显示Markdown格式提示
- [ ] 保存卡片
- [ ] **预期结果**：卡片创建成功

### 4. 卡片编辑功能测试

#### 4.1 编辑现有卡片
- [ ] 在书籍详情页，点击任意卡片的编辑按钮
- [ ] **预期结果**：进入编辑模式，表单已填充现有数据
- [ ] 修改标题为："更新后的标题"
- [ ] 修改问题为："更新后的问题"
- [ ] **预期结果**：预览实时更新
- [ ] 点击保存
- [ ] **预期结果**：显示"卡片更新成功"提示

### 5. 同步功能测试

#### 5.1 手动同步触发
- [ ] 在任意页面查看同步状态指示器
- [ ] **预期结果**：显示当前同步状态（成功/失败/同步中）
- [ ] 点击手动同步按钮
- [ ] **预期结果**：显示同步进度，完成后显示结果

#### 5.2 数据一致性验证
- [ ] 在创建/编辑卡片后，检查网络请求日志
- [ ] **预期结果**：确认数据已发送到服务器
- [ ] 在其他设备登录同一账号
- [ ] **预期结果**：新创建/编辑的卡片已同步显示

### 6. 错误处理测试

#### 6.1 网络断开测试
- [ ] 断开网络连接
- [ ] 创建新卡片并保存
- [ ] **预期结果**：卡片在本地创建成功
- [ ] **预期结果**：同步状态显示为失败或待同步
- [ ] 恢复网络连接
- [ ] 触发手动同步
- [ ] **预期结果**：本地数据同步到服务器

#### 6.2 输入验证测试
- [ ] 尝试创建空标题的卡片
- [ ] **预期结果**：显示相应的验证错误信息
- [ ] 尝试创建空问题的卡片
- [ ] **预期结果**：显示相应的验证错误信息

### 7. 用户体验测试

#### 7.1 界面响应性
- [ ] 在不同输入框间切换
- [ ] **预期结果**：界面响应流畅，无卡顿
- [ ] 快速输入大量文本
- [ ] **预期结果**：预览更新及时，无延迟

#### 7.2 类型切换测试
- [ ] 在创建卡片时切换不同类型
- [ ] **预期结果**：编辑界面根据类型变化，显示相应的提示和字段
- [ ] **预期结果**：预览样式随类型变化

## 测试结果记录

### 通过的测试用例
- [ ] 书籍同步功能
- [ ] 基础卡片创建
- [ ] 实时预览功能
- [ ] 不同卡片类型创建
- [ ] 卡片编辑功能
- [ ] 手动同步功能
- [ ] 错误处理
- [ ] 用户体验

### 发现的问题
记录测试过程中发现的任何问题：

1. 问题描述：
   - 重现步骤：
   - 预期结果：
   - 实际结果：
   - 严重程度：

2. 问题描述：
   - 重现步骤：
   - 预期结果：
   - 实际结果：
   - 严重程度：

## 测试总结

### 功能完成度
- 核心功能：✅ 完成
- 同步功能：✅ 完成  
- 预览功能：✅ 完成
- 类型支持：✅ 完成
- 错误处理：✅ 完成

### 建议改进
1. 可以添加更多卡片类型
2. 可以优化预览界面的样式
3. 可以添加批量操作功能
4. 可以添加卡片模板功能

### 测试结论
□ 所有功能正常，可以发布
□ 存在小问题，建议修复后发布  
□ 存在严重问题，需要修复后重新测试
